# Terraform and Terragrunt files
*.tfstate
*.tfstate.*
*.tfplan
*.tfvars
.terraform/
.terragrunt-cache/
.terraform.lock.hcl

# Sensitive configuration files
# Uncomment the following lines if you want to exclude the entire common-vars.yaml
# common-vars.yaml
# *.yaml

# OCI CLI configuration (contains sensitive information)
.oci/
*.pem
*.key

# Environment variables
.env
.env.local
.env.*.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp

# Backup files
*.bak
*.backup

# Node modules (if using any Node.js tools)
node_modules/

# Python files (if using any Python tools)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Local development overrides
local.yaml
local-*.yaml
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Terragrunt debug files
terragrunt-debug.tfvars.json

# Crash log files
crash.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.tfvars
*.tfvars.json

# Ignore CLI configuration files
.terraformrc
terraform.rc

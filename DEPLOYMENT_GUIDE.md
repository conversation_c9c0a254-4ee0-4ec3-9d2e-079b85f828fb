# Oracle Cloud Infrastructure Deployment Guide

This guide provides detailed instructions for deploying all infrastructure modules in the correct order.

## 📋 Prerequisites

1. **Install Prerequisites**:
   ```bash
   ./install.sh
   ```

2. **Configure OCI CLI**:
   ```bash
   oci setup config
   ```

3. **Update Configuration**:
   ```bash
   cp common-vars.yaml.example common-vars.yaml
   # Edit common-vars.yaml with your OCI details
   ```

## 🚀 Deployment Order

### 1. Network Infrastructure (Foundation)

The network module must be deployed first as all other modules depend on it.

```bash
cd infrastructure/network
terragrunt init
terragrunt plan
terragrunt apply
cd ../..
```

**What gets created:**
- VCN with 10.0.0.0/16 CIDR
- 3 Public subnets across availability domains
- 3 Private subnets across availability domains
- Internet Gateway, NAT Gateway, Service Gateway
- Security Lists with pre-configured ports

### 2. PostgreSQL Database

Deploy the PostgreSQL database in private subnets.

```bash
cd infrastructure/postgresql
terragrunt init
terragrunt plan
terragrunt apply
cd ../..
```

**What gets created:**
- OCI Database System with PostgreSQL
- Automated backup configuration
- Network Security Groups
- Database monitoring setup

**Configuration Notes:**
- Update `ssh_public_key` in `infrastructure/postgresql/terragrunt.hcl`
- Set a secure `db_password` or leave empty for auto-generation
- Adjust `storage_size_gb` based on your needs

### 3. Kubernetes Cluster (OKE)

Deploy the Kubernetes cluster with node pools.

```bash
cd infrastructure/kubernetes
terragrunt init
terragrunt plan
terragrunt apply
cd ../..
```

**What gets created:**
- OKE cluster with public endpoint
- Node pool with 3 worker nodes (configurable)
- Network Security Groups for cluster communication
- Kubeconfig for cluster access

**Configuration Notes:**
- Update `ssh_public_key` for worker node access
- Adjust `node_pool_config` for your workload requirements
- Configure `api_access_cidr` to restrict API access

### 4. Cluster Autoscaler

Deploy the cluster autoscaler for automatic node scaling.

```bash
cd infrastructure/cluster-autoscaler
terragrunt init
terragrunt plan
terragrunt apply
cd ../..
```

**What gets created:**
- Kubernetes namespace for autoscaler
- Service account with proper RBAC permissions
- Cluster autoscaler deployment
- Scaling policies and configurations

**Configuration Notes:**
- Adjust `min_nodes` and `max_nodes` for your scaling needs
- Configure scaling thresholds in `terragrunt.hcl`
- Monitor autoscaler logs: `kubectl logs -n kube-system deployment/cluster-autoscaler`

### 5. Load Balancer

Deploy the load balancer for traffic distribution.

```bash
cd infrastructure/load-balancer
terragrunt init
terragrunt plan
terragrunt apply
cd ../..
```

**What gets created:**
- OCI Load Balancer in public subnets
- Backend sets with health checks
- HTTP/HTTPS listeners
- SSL certificate management (if configured)

**Configuration Notes:**
- Add your backend servers to `backend_sets` configuration
- Configure SSL certificates for HTTPS
- Set up path-based routing if needed

## 🔧 Post-Deployment Configuration

### Access Kubernetes Cluster

1. **Get kubeconfig**:
   ```bash
   cd infrastructure/kubernetes
   terragrunt output kubeconfig > ~/.kube/config
   ```

2. **Verify cluster access**:
   ```bash
   kubectl get nodes
   kubectl get pods --all-namespaces
   ```

### Configure Load Balancer Backends

1. **Update backend configuration**:
   ```bash
   # Edit infrastructure/load-balancer/terragrunt.hcl
   # Add your application server IPs to backend_sets
   ```

2. **Apply changes**:
   ```bash
   cd infrastructure/load-balancer
   terragrunt apply
   ```

### Monitor Cluster Autoscaler

1. **Check autoscaler status**:
   ```bash
   kubectl get deployment cluster-autoscaler -n kube-system
   kubectl logs -f deployment/cluster-autoscaler -n kube-system
   ```

2. **Test scaling**:
   ```bash
   # Deploy a test workload to trigger scaling
   kubectl create deployment test-app --image=nginx --replicas=10
   kubectl get nodes -w
   ```

## 🔍 Verification Commands

### Check All Resources

```bash
# Network resources
cd infrastructure/network && terragrunt output

# Database status
cd ../postgresql && terragrunt output

# Kubernetes cluster
cd ../kubernetes && terragrunt output

# Autoscaler status
cd ../cluster-autoscaler && terragrunt output

# Load balancer
cd ../load-balancer && terragrunt output
```

### Health Checks

```bash
# Database connectivity
oci db database list --compartment-id <compartment-id>

# Kubernetes cluster health
kubectl cluster-info
kubectl get componentstatuses

# Load balancer status
oci lb load-balancer list --compartment-id <compartment-id>
```

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Errors**:
   ```bash
   oci iam user get --user-id <your-user-ocid>
   ```

2. **Network Connectivity**:
   ```bash
   # Check security list rules
   oci network security-list list --compartment-id <compartment-id>
   ```

3. **Kubernetes Access**:
   ```bash
   # Regenerate kubeconfig
   oci ce cluster create-kubeconfig --cluster-id <cluster-id> --file ~/.kube/config
   ```

4. **Autoscaler Issues**:
   ```bash
   # Check autoscaler logs
   kubectl logs deployment/cluster-autoscaler -n kube-system
   ```

### Cleanup

To destroy all resources (in reverse order):

```bash
# Destroy load balancer
cd infrastructure/load-balancer && terragrunt destroy

# Destroy cluster autoscaler
cd ../cluster-autoscaler && terragrunt destroy

# Destroy Kubernetes cluster
cd ../kubernetes && terragrunt destroy

# Destroy PostgreSQL
cd ../postgresql && terragrunt destroy

# Destroy network (last)
cd ../network && terragrunt destroy
```

## 📚 Next Steps

1. **Configure monitoring** with OCI Monitoring and Logging
2. **Set up CI/CD pipelines** for application deployment
3. **Implement backup strategies** for critical data
4. **Configure alerting** for infrastructure health
5. **Set up log aggregation** for troubleshooting

# Oracle Cloud Infrastructure Terraform/Terragrunt Makefile
# Provides convenient commands for common operations

.PHONY: help install plan apply destroy clean validate format check-config

# Default target
help: ## Show this help message
	@echo "Oracle Cloud Infrastructure Terraform/Terragrunt Commands"
	@echo "=========================================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install prerequisites (Terraform, Terragrunt, OCI CLI)
	@echo "Installing prerequisites..."
	./install.sh

check-config: ## Validate OCI configuration
	@echo "Checking OCI configuration..."
	@oci iam user get --user-id $$(oci iam user list --query 'data[0].id' --raw-output) || echo "OCI CLI not configured properly"

validate: ## Validate all Terragrunt configurations
	@echo "Validating Terragrunt configurations..."
	terragrunt run-all validate

format: ## Format all Terraform files
	@echo "Formatting Terraform files..."
	terraform fmt -recursive .

plan: ## Plan all infrastructure changes
	@echo "Planning infrastructure changes..."
	terragrunt run-all plan

plan-network: ## Plan network module changes
	@echo "Planning network module changes..."
	cd infrastructure/network && terragrunt plan

plan-postgresql: ## Plan PostgreSQL module changes
	@echo "Planning PostgreSQL module changes..."
	cd infrastructure/postgresql && terragrunt plan

plan-kubernetes: ## Plan Kubernetes module changes
	@echo "Planning Kubernetes module changes..."
	cd infrastructure/kubernetes && terragrunt plan

apply: ## Apply all infrastructure changes
	@echo "Applying infrastructure changes..."
	@echo "WARNING: This will create/modify infrastructure resources!"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo ""; \
		terragrunt run-all apply; \
	else \
		echo ""; \
		echo "Cancelled."; \
	fi

apply-network: ## Apply network module changes
	@echo "Applying network module changes..."
	cd infrastructure/network && terragrunt apply

apply-postgresql: ## Apply PostgreSQL module changes
	@echo "Applying PostgreSQL module changes..."
	cd infrastructure/postgresql && terragrunt apply

apply-kubernetes: ## Apply Kubernetes module changes
	@echo "Applying Kubernetes module changes..."
	cd infrastructure/kubernetes && terragrunt apply

destroy: ## Destroy all infrastructure (DANGEROUS!)
	@echo "WARNING: This will DESTROY all infrastructure resources!"
	@echo "This action cannot be undone!"
	@read -p "Type 'yes' to confirm destruction: " confirm; \
	if [ "$$confirm" = "yes" ]; then \
		terragrunt run-all destroy; \
	else \
		echo "Destruction cancelled."; \
	fi

destroy-kubernetes: ## Destroy Kubernetes module
	@echo "Destroying Kubernetes module..."
	cd infrastructure/kubernetes && terragrunt destroy

destroy-postgresql: ## Destroy PostgreSQL module
	@echo "Destroying PostgreSQL module..."
	cd infrastructure/postgresql && terragrunt destroy

destroy-network: ## Destroy network module (DANGEROUS - destroys foundation!)
	@echo "WARNING: Destroying network will affect all dependent resources!"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo ""; \
		cd infrastructure/network && terragrunt destroy; \
	else \
		echo ""; \
		echo "Cancelled."; \
	fi

output: ## Show outputs from all modules
	@echo "Network outputs:"
	@cd infrastructure/network && terragrunt output || echo "Network not deployed"
	@echo ""
	@echo "PostgreSQL outputs:"
	@cd infrastructure/postgresql && terragrunt output || echo "PostgreSQL not deployed"
	@echo ""
	@echo "Kubernetes outputs:"
	@cd infrastructure/kubernetes && terragrunt output || echo "Kubernetes not deployed"

clean: ## Clean Terragrunt cache and temporary files
	@echo "Cleaning Terragrunt cache..."
	find . -type d -name ".terragrunt-cache" -exec rm -rf {} + 2>/dev/null || true
	find . -name "*.tfplan" -delete 2>/dev/null || true
	find . -name ".terraform.lock.hcl" -delete 2>/dev/null || true

init: ## Initialize all modules
	@echo "Initializing all modules..."
	terragrunt run-all init

refresh: ## Refresh state for all modules
	@echo "Refreshing state for all modules..."
	terragrunt run-all refresh

show: ## Show current state
	@echo "Showing current state..."
	terragrunt run-all show

# Development helpers
dev-setup: install check-config ## Complete development setup
	@echo "Development environment setup complete!"
	@echo "Next steps:"
	@echo "1. Update common-vars.yaml with your OCI configuration"
	@echo "2. Run 'make plan' to see what will be created"
	@echo "3. Run 'make apply' to create the infrastructure"

quick-deploy: validate plan apply ## Quick deployment (validate, plan, apply)
	@echo "Quick deployment complete!"

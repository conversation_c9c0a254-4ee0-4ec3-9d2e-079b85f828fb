# Quick Start Guide - Oracle Cloud Infrastructure

## 🚀 Get Started in 5 Minutes

### Step 1: Install Prerequisites
```bash
chmod +x install.sh
./install.sh
```

### Step 2: Configure Your Environment
```bash
# Copy the example configuration
cp common-vars.yaml.example common-vars.yaml

# Edit with your OCI details (required!)
nano common-vars.yaml  # or use your preferred editor
```

**Required Configuration:**
- `tenancy_ocid`: Your OCI tenancy OCID
- `user_ocid`: Your OCI user OCID  
- `compartment_ocid`: Your compartment OCID
- `private_key_path`: Path to your OCI private key
- `fingerprint`: Your OCI key fingerprint
- `region`: Your OCI region (e.g., "us-ashburn-1")

### Step 3: Deploy Infrastructure

**Option A: Deploy All Modules at Once**
```bash
terragrunt run-all init
terragrunt run-all plan
terragrunt run-all apply
```

**Option B: Deploy Modules Individually**
```bash
# Deploy network first (foundation)
cd infrastructure/network
terragrunt init
terragrunt plan
terragrunt apply
cd ../..

# Deploy PostgreSQL (when implemented)
cd infrastructure/postgresql
terragrunt init
terragrunt plan
terragrunt apply
cd ../..

# Deploy Kubernetes (when implemented)
cd infrastructure/kubernetes
terragrunt init
terragrunt plan
terragrunt apply
cd ../..
```

### Step 4: Verify Deployment
```bash
# Check network outputs
cd infrastructure/network
terragrunt output
cd ../..

# Or check all outputs
terragrunt run-all output
```

## 🏗️ What Gets Created

### Network Module (Ready to Deploy)
- **VCN** with 10.0.0.0/16 CIDR
- **3 Public Subnets** (10.0.1.0/24, 10.0.2.0/24, 10.0.3.0/24)
- **3 Private Subnets** (10.0.10.0/24, 10.0.11.0/24, 10.0.12.0/24)
- **Internet Gateway** for public access
- **NAT Gateway** for private subnet outbound
- **Security Lists** with pre-configured ports:
  - Web: 80, 443
  - SSH: 22
  - Database: 5432
  - Kubernetes: 6443, 2379-2380, 10250-10252
  - Application: 8080, 8443, 9090

### PostgreSQL Module (Placeholder)
- Structure ready for OCI Database System
- Variables configured for database setup
- Dependencies properly configured

### Kubernetes Module (Placeholder)
- Structure ready for OKE cluster
- Variables configured for cluster setup
- Dependencies properly configured

## 🔧 Common Commands

```bash
# Plan changes for all modules
terragrunt run-all plan

# Apply changes for all modules
terragrunt run-all apply

# Show outputs from all modules
terragrunt run-all output

# Destroy all modules (DANGEROUS!)
terragrunt run-all destroy

# Work with individual modules
cd infrastructure/network
terragrunt plan
terragrunt apply
terragrunt output
terragrunt destroy
```

## 🆘 Troubleshooting

**Authentication Issues:**
```bash
# Test OCI CLI configuration
oci iam user get --user-id $(oci iam user list --query 'data[0].id' --raw-output)

# Check private key permissions
chmod 600 ~/.oci/oci_api_key.pem
```

**Clean Cache:**
```bash
# Remove Terragrunt cache
find . -type d -name ".terragrunt-cache" -exec rm -rf {} + 2>/dev/null || true
```

**Debug Mode:**
```bash
export TF_LOG=DEBUG
export TERRAGRUNT_LOG_LEVEL=debug
terragrunt plan
```

## 📁 Project Structure
```
oracle-infra/
├── common-vars.yaml              # Your configuration (edit this!)
├── common-vars.yaml.example      # Example configuration
├── terragrunt.hcl               # Root Terragrunt config
├── install.sh                   # Prerequisites installer
├── modules/                     # Terraform modules
│   ├── network/                 # ✅ Network module (complete)
│   ├── postgresql/              # 🚧 PostgreSQL (placeholder)
│   └── kubernetes/              # 🚧 Kubernetes (placeholder)
└── infrastructure/              # Deployment configs
    ├── network/                 # Network deployment
    ├── postgresql/              # PostgreSQL deployment
    └── kubernetes/              # Kubernetes deployment
```

## 🎯 Next Steps

1. **Deploy the network module** - it's ready to use!
2. **Implement PostgreSQL module** - update `modules/postgresql/main.tf`
3. **Implement Kubernetes module** - update `modules/kubernetes/main.tf`
4. **Add more modules** - compute instances, load balancers, etc.

For detailed information, see the main [README.md](README.md) file.

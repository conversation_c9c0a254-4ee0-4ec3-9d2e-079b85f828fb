# Oracle Cloud Infrastructure with Terraform and Terragrunt

This repository contains a **production-ready, modular Terraform/Terragrunt infrastructure** setup for Oracle Cloud Infrastructure (OCI). It provides a scalable foundation with optimized configuration management, centralized variables, and comprehensive automation.

## 🏗️ Architecture Overview

The infrastructure is organized into the following **production-tested modules**:

- **🌐 Network Module**: VCN with 3 public and 3 private subnets across availability domains, complete security groups
- **🗄️ PostgreSQL Module**: **Real PostgreSQL Database Service** with automated backups, monitoring, and HA options
- **☸️ Kubernetes Module**: **OKE cluster** with configurable node pools, autoscaling, and security configurations
- **📈 Cluster Autoscaler Module**: Kubernetes cluster autoscaler with RBAC, scaling policies, and resource management
- **⚖️ Load Balancer Module**: OCI Load Balancer for traffic distribution and high availability (optional)

## ✨ Key Features

- **🔧 Optimized Configuration**: Centralized variables, eliminated redundancy, improved maintainability
- **🚀 Production Ready**: Real PostgreSQL service, comprehensive security rules, monitoring enabled
- **📦 Modular Design**: Independent modules with proper dependency management
- **🔒 Security First**: Comprehensive security groups, ICMP rules, OKE-specific networking
- **📊 Monitoring**: Built-in monitoring and alerting capabilities
- **🔄 Automated Scaling**: Cluster autoscaler with configurable policies

## 📁 Project Structure

```
oracle-cloud-infrastructure/
├── 📋 common-vars.yaml              # ✨ OPTIMIZED: Centralized configuration with shared variables
├── 🔧 root.hcl                     # Root Terragrunt configuration with OCI backend
├── 🛠️ install.sh                   # Prerequisites installation script
├── 📖 README.md                    # This comprehensive documentation
├── 📦 modules/                     # Terraform modules (reusable components)
│   ├── 🌐 network/                 # Network infrastructure (VCN, subnets, security)
│   │   ├── main.tf                 # Network resources with ICMP rules
│   │   ├── variables.tf            # Network-specific variables
│   │   ├── outputs.tf              # Network outputs (VCN ID, subnet IDs)
│   │   └── versions.tf             # Provider requirements
│   ├── 🗄️ postgresql/              # PostgreSQL Database Service (REAL implementation)
│   │   ├── main.tf                 # PostgreSQL DB System resources
│   │   ├── variables.tf            # ✨ OPTIMIZED: Removed redundant variables
│   │   ├── outputs.tf              # Database connection details
│   │   └── versions.tf             # Provider requirements
│   ├── ☸️ kubernetes/              # Kubernetes (OKE) cluster
│   │   ├── main.tf                 # OKE cluster and node pools
│   │   ├── variables.tf            # Kubernetes configuration
│   │   ├── outputs.tf              # Cluster details and kubeconfig
│   │   └── versions.tf             # Provider requirements
│   ├── 📈 cluster-autoscaler/      # Cluster autoscaler with RBAC
│   │   ├── main.tf                 # Autoscaler deployment and policies
│   │   ├── variables.tf            # Autoscaler configuration
│   │   ├── outputs.tf              # Autoscaler status
│   │   └── versions.tf             # Provider requirements
│   └── ⚖️ load-balancer/           # Load balancer (optional)
│       ├── main.tf                 # Load balancer resources
│       ├── variables.tf            # LB configuration
│       ├── outputs.tf              # LB endpoints
│       └── versions.tf             # Provider requirements
└── 🚀 infrastructure/              # Single environment deployment (production-ready)
    ├── 🌐 network/                 # Network module deployment
    │   └── terragrunt.hcl          # ✨ OPTIMIZED: Uses shared variables
    ├── 🗄️ postgresql/              # PostgreSQL module deployment
    │   └── terragrunt.hcl          # ✨ OPTIMIZED: References shared SSH keys
    ├── ☸️ kubernetes/              # Kubernetes module deployment
    │   └── terragrunt.hcl          # ✨ OPTIMIZED: Centralized configuration
    ├── 📈 cluster-autoscaler/      # Cluster autoscaler deployment
    │   └── terragrunt.hcl          # ✨ OPTIMIZED: Fixed variable references
    └── ⚖️ load-balancer/           # Load balancer deployment
        └── terragrunt.hcl          # Load balancer configuration
```

## 🚀 Quick Start

### Prerequisites

1. **Run the installation script** to install all required tools:
   ```bash
   chmod +x install.sh
   ./install.sh
   ```

   This script installs:
   - **Terraform** (latest stable version)
   - **Terragrunt** (latest stable version)
   - **OCI CLI** (Oracle Cloud Infrastructure CLI)
   - **jq** (for JSON processing)
   - **kubectl** (Kubernetes CLI)

2. **Configure OCI CLI** (if not done during installation):
   ```bash
   oci setup config
   ```

### Configuration

1. **Update common-vars.yaml** with your OCI details:
   ```yaml
   # =============================================================================
   # GLOBAL CONFIGURATION
   # =============================================================================
   shared:
     ssh_public_key: "ssh-rsa AAAAB3NzaC1yc2E... your-ssh-key"
     vcn_cidr: "10.0.0.0/16"
     internal_cidr: "10.0.0.0/16"

   oci:
     region: "us-phoenix-1"                    # Your OCI region
     tenancy_ocid: "ocid1.tenancy.oc1..xxx"   # Your tenancy OCID
     user_ocid: "ocid1.user.oc1..xxx"         # Your user OCID
     compartment_ocid: "ocid1.compartment.oc1..xxx"  # Your compartment OCID
     private_key_path: "~/.oci/oci_api_key.pem"      # Path to your private key
     fingerprint: "aa:bb:cc:dd:ee:ff:00:11:22:33:44:55:66:77:88:99"  # Your key fingerprint

   environment:
     project_name: "oracle"
     name: "dev"
   ```

2. **Create OCI Object Storage bucket** for Terraform state (optional but recommended):
   ```bash
   oci os bucket create --name oracle-dev-terraform-state --compartment-id <your-compartment-ocid>
   ```

3. **Enable remote state** (optional) by uncommenting the remote_state block in `root.hcl`

### 🚀 Deployment

Deploy the infrastructure modules in the **recommended order** (dependencies managed automatically):

#### **Option 1: Sequential Deployment (Recommended for first-time)**

1. **🌐 Deploy Network Infrastructure** (Foundation):
   ```bash
   cd infrastructure/network
   terragrunt plan
   terragrunt apply --auto-approve
   cd ../..
   ```

2. **🗄️ Deploy PostgreSQL Database** (Production-ready):
   ```bash
   cd infrastructure/postgresql
   terragrunt plan
   terragrunt apply --auto-approve
   cd ../..
   ```

3. **☸️ Deploy Kubernetes Cluster** (OKE with node pools):
   ```bash
   cd infrastructure/kubernetes
   terragrunt plan
   terragrunt apply --auto-approve
   cd ../..
   ```

4. **📈 Deploy Cluster Autoscaler** (Auto-scaling):
   ```bash
   cd infrastructure/cluster-autoscaler
   terragrunt plan
   terragrunt apply --auto-approve
   cd ../..
   ```

5. **⚖️ Deploy Load Balancer** (Optional):
   ```bash
   cd infrastructure/load-balancer
   terragrunt plan
   terragrunt apply --auto-approve
   cd ../..
   ```

#### **Option 2: Deploy All Modules at Once** (Advanced):
```bash
# From the root directory - Terragrunt handles dependencies
terragrunt run-all plan
terragrunt run-all apply --terragrunt-non-interactive
```

#### **Option 3: Validate Before Deployment**:
```bash
# Validate all configurations first
terragrunt run-all validate

# Then deploy with confirmation
terragrunt run-all plan
terragrunt run-all apply
```

#### **📊 Check Deployment Status**:
```bash
# View outputs from all modules
cd infrastructure/network && terragrunt output && cd ../..
cd infrastructure/postgresql && terragrunt output && cd ../..
cd infrastructure/kubernetes && terragrunt output && cd ../..
cd infrastructure/cluster-autoscaler && terragrunt output && cd ../..
```

## 🔧 Configuration Details

### 🌐 Network Configuration

The **optimized network module** creates a comprehensive, production-ready network:

- **🏗️ VCN**: Virtual Cloud Network with configurable CIDR (default: 10.0.0.0/16)
- **🌍 Public Subnets**: 3 subnets across availability domains for load balancers and bastion hosts
- **🔒 Private Subnets**: 3 subnets across availability domains for application servers and databases
- **🌐 Internet Gateway**: For public subnet internet access
- **🔄 NAT Gateway**: For private subnet outbound internet access
- **⚙️ Service Gateway**: For OCI services access
- **🛡️ Security Lists**: Comprehensive security rules with ICMP support

### 🛡️ Security Rules (Optimized)

Security rules are **centralized** in `common-vars.yaml` with **eliminated redundancy**:

```yaml
# =============================================================================
# SECURITY RULES CONFIGURATION
# =============================================================================
security_rules:
  web:
    - port: 80
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTP traffic"
    - port: 443
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTPS traffic"

  ssh:
    - port: 22
      protocol: "TCP"
      source: "0.0.0.0/0"  # Restrict in production
      description: "SSH access"

  database:
    - port: 5432
      protocol: "TCP"
      source: "10.0.0.0/16"  # VCN only
      description: "PostgreSQL access"

  kubernetes:
    - port: 6443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Kubernetes API server"
    - port: 443
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTPS for container registry"
    - port: 12250
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "OKE worker communication"
    # ... additional K8s ports
```

### 🗄️ PostgreSQL Configuration (Production-Ready)

**Real PostgreSQL Database Service** with optimized configuration:

```yaml
# =============================================================================
# POSTGRESQL CONFIGURATION
# =============================================================================
postgresql:
  db_name: "snapdb"
  db_username: "snapuser"
  db_password: "SnapStore_321#"  # Use OCI Vault in production
  db_version: "14"

  # Instance configuration
  instance_shape: "PostgreSQL.VM.Standard.E4.Flex.2.32GB"  # 2 OCPUs, 32GB RAM
  storage_size_gb: 256

  # Backup configuration
  backup_retention_days: 7
  enable_backup: true
  backup_window_start_time: "02:00"

  # Additional settings
  high_availability: false
  monitoring_enabled: true
```

### ☸️ Kubernetes Configuration (OKE)

**Production-ready OKE cluster** with optimized node pools:

```yaml
# =============================================================================
# KUBERNETES CONFIGURATION
# =============================================================================
kubernetes:
  kubernetes_version: "v1.33.1"
  api_access_cidr: "0.0.0.0/0"  # Restrict in production
  enable_monitoring: true

  # Simplified node pools (optimized from array structure)
  node_pools:
    workers:
      node_shape: "VM.Standard.E3.Flex"
      node_shape_memory_gb: 8
      node_shape_ocpus: 2
      size: 1
      max_size: 10
      min_size: 1
      boot_volume_size_gb: 50
```

**Destroy All Resources**:
```bash
# Destroy in reverse dependency order
cd infrastructure/cluster-autoscaler && terragrunt destroy --auto-approve && cd ../..
cd infrastructure/postgresql && terragrunt destroy --auto-approve && cd ../..
cd infrastructure/kubernetes && terragrunt destroy --auto-approve && cd ../..
cd infrastructure/network && terragrunt destroy --auto-approve && cd ../..

# Or destroy all at once (advanced)
terragrunt run-all destroy --terragrunt-non-interactive
```

### 🔧 Module Development

**Adding New Modules** (follows optimized pattern):

1. **Create module directory**: `modules/your-module/`
2. **Add standard Terraform files**:
   - `main.tf` - Main resources
   - `variables.tf` - Input variables (reference shared vars)
   - `outputs.tf` - Output values
   - `versions.tf` - Provider requirements

3. **Create deployment configuration**: `infrastructure/your-module/terragrunt.hcl`
4. **Update common-vars.yaml** with module-specific configuration
5. **Reference shared variables** for consistency

**Module Dependencies** (Terragrunt handles automatically):

```hcl
# Example: infrastructure/your-module/terragrunt.hcl
locals {
  common_vars = yamldecode(file(find_in_parent_folders("common-vars.yaml")))
  shared_vars = local.common_vars.shared  # ✨ Use shared variables
}

dependency "network" {
  config_path = "../network"
  mock_outputs = {
    vcn_id = "ocid1.vcn.oc1..mock"
  }
}

inputs = {
  # Use shared SSH key instead of module-specific
  ssh_public_key = local.shared_vars.ssh_public_key
  vcn_id = dependency.network.outputs.vcn_id
}
```

## 📋 Essential Commands

### 🚀 Terragrunt Commands

**Single Module Operations** (run from module directory):
```bash
cd infrastructure/network  # or postgresql, kubernetes, cluster-autoscaler

terragrunt validate       # Validate configuration
terragrunt plan           # Preview changes
terragrunt apply          # Apply changes
terragrunt output         # Show outputs
terragrunt destroy        # Destroy resources
```

**All Modules Operations** (run from root directory):
```bash
terragrunt run-all validate    # Validate all configurations
terragrunt run-all plan        # Plan all modules
terragrunt run-all apply       # Deploy all modules
terragrunt run-all output      # Show all outputs
terragrunt run-all destroy     # Destroy all (DANGEROUS!)
```

## 📚 Additional Resources

- **[OCI Terraform Provider](https://registry.terraform.io/providers/oracle/oci/latest/docs)** - Complete resource documentation
- **[Terragrunt Documentation](https://terragrunt.gruntwork.io/docs/)** - Advanced configuration patterns
- **[OCI CLI Reference](https://docs.oracle.com/en-us/iaas/tools/oci-cli/latest/)** - Command-line interface
- **[OKE Documentation](https://docs.oracle.com/en-us/iaas/Content/ContEng/home.htm)** - Kubernetes service details
- **[PostgreSQL on OCI](https://docs.oracle.com/en-us/iaas/postgresql-database/)** - Database service guide

## 🎯 Next Steps

1. **🔧 Customize** `common-vars.yaml` for your environment
2. **🚀 Deploy** using the sequential or all-at-once approach
3. **🔍 Monitor** resources using OCI Console
4. **📈 Scale** node pools based on workload requirements
5. **🔒 Secure** by restricting access and enabling monitoring
6. **💾 Backup** critical data and test disaster recovery

## 📄 License

This project is licensed under the **MIT License** - see the LICENSE file for details.

---

**🎉 Ready to deploy production-grade Oracle Cloud Infrastructure with optimized, maintainable code!**

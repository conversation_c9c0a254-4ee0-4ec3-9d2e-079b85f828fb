# Oracle Cloud Infrastructure with Terraform and Terragrunt

This repository contains a modular Terraform/Terragrunt infrastructure setup for Oracle Cloud Infrastructure (OCI). It provides a scalable foundation with network, PostgreSQL database, and Kubernetes cluster modules.

## 🏗️ Architecture Overview

The infrastructure is organized into the following modules:

- **Network Module**: VCN with 3 public and 3 private subnets across availability domains
- **PostgreSQL Module**: Database system for application data (placeholder - to be implemented)
- **Kubernetes Module**: OKE cluster for container orchestration (placeholder - to be implemented)

## 📁 Project Structure

```
.
├── common-vars.yaml              # Shared configuration variables
├── terragrunt.hcl               # Root Terragrunt configuration
├── install.sh                   # Prerequisites installation script
├── modules/                     # Terraform modules
│   ├── network/                 # Network infrastructure module
│   ├── postgresql/              # PostgreSQL database module (placeholder)
│   └── kubernetes/              # Kubernetes cluster module (placeholder)
└── infrastructure/              # Single environment deployment
    ├── network/                 # Network module deployment
    ├── postgresql/              # PostgreSQL module deployment
    └── kubernetes/              # Kubernetes module deployment
```

## 🚀 Quick Start

### Prerequisites

1. **Run the installation script** to install all required tools:
   ```bash
   chmod +x install.sh
   ./install.sh
   ```

   This script installs:
   - Terraform (latest stable version)
   - Terragrunt (latest stable version)
   - OCI CLI
   - jq (for JSON processing)

2. **Configure OCI CLI** (if not done during installation):
   ```bash
   oci setup config
   ```

### Configuration

1. **Update common-vars.yaml** with your OCI details:
   ```yaml
   oci:
     region: "us-ashburn-1"                    # Your OCI region
     tenancy_ocid: "ocid1.tenancy.oc1..xxx"   # Your tenancy OCID
     user_ocid: "ocid1.user.oc1..xxx"         # Your user OCID
     compartment_ocid: "ocid1.compartment.oc1..xxx"  # Your compartment OCID
     private_key_path: "~/.oci/oci_api_key.pem"      # Path to your private key
     fingerprint: "aa:bb:cc:dd:ee:ff:00:11:22:33:44:55:66:77:88:99"  # Your key fingerprint
   ```

2. **Create OCI Object Storage bucket** for Terraform state (optional but recommended):
   ```bash
   oci os bucket create --name oracle-infra-production-terraform-state --compartment-id <your-compartment-ocid>
   ```

### Deployment

Deploy the infrastructure modules in order:

1. **Deploy Network Infrastructure**:
   ```bash
   cd infrastructure/network
   terragrunt plan
   terragrunt apply
   cd ../..
   ```

2. **Deploy PostgreSQL** (when implemented):
   ```bash
   cd infrastructure/postgresql
   terragrunt plan
   terragrunt apply
   cd ../..
   ```

3. **Deploy Kubernetes Cluster** (when implemented):
   ```bash
   cd infrastructure/kubernetes
   terragrunt plan
   terragrunt apply
   cd ../..
   ```

4. **Deploy All Modules at Once**:
   ```bash
   # From the root directory
   terragrunt run-all plan
   terragrunt run-all apply
   ```

5. **Check Deployment Status**:
   ```bash
   # View outputs from all modules
   cd infrastructure/network && terragrunt output
   cd ../postgresql && terragrunt output
   cd ../kubernetes && terragrunt output
   cd ../..
   ```

## 🔧 Configuration Details

### Network Configuration

The network module creates:
- **VCN**: Virtual Cloud Network with configurable CIDR (default: 10.0.0.0/16)
- **Public Subnets**: 3 subnets across availability domains for load balancers and bastion hosts
- **Private Subnets**: 3 subnets across availability domains for application servers and databases
- **Internet Gateway**: For public subnet internet access
- **NAT Gateway**: For private subnet outbound internet access
- **Service Gateway**: For OCI services access
- **Security Lists**: Configured with the following ports:
  - **Web Traffic**: 80, 443
  - **SSH**: 22
  - **Database**: 5432 (PostgreSQL)
  - **Kubernetes**: 6443, 2379-2380, 10250-10252
  - **Application**: 8080, 8443, 9090

### Security Rules

Security rules are defined in `common-vars.yaml` and can be customized:

```yaml
security_rules:
  web:
    - port: 80
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTP traffic"
  # ... more rules
```

## 🛠️ Module Development

### Adding New Modules

1. Create module directory: `modules/your-module/`
2. Add standard Terraform files:
   - `main.tf` - Main resources
   - `variables.tf` - Input variables
   - `outputs.tf` - Output values
   - `versions.tf` - Provider requirements

3. Create deployment configuration: `infrastructure/your-module/terragrunt.hcl`

### Module Dependencies

Use Terragrunt dependencies to ensure proper deployment order:

```hcl
dependency "network" {
  config_path = "../network"
  
  mock_outputs = {
    vcn_id = "ocid1.vcn.oc1..mock"
  }
}

inputs = {
  vcn_id = dependency.network.outputs.vcn_id
}
```

## 📋 Available Commands

### Terragrunt Commands

**Single Module Operations** (run from module directory):
```bash
cd infrastructure/network  # or postgresql, kubernetes

# Plan changes for the current module
terragrunt plan

# Apply changes for the current module
terragrunt apply

# Destroy the current module
terragrunt destroy

# Show outputs from the current module
terragrunt output

# Validate the current module configuration
terragrunt validate

# Initialize the current module
terragrunt init

# Refresh state for the current module
terragrunt refresh
```

**All Modules Operations** (run from root directory):
```bash
# Plan changes for all modules
terragrunt run-all plan

# Apply changes for all modules
terragrunt run-all apply

# Destroy all modules (DANGEROUS!)
terragrunt run-all destroy

# Show outputs from all modules
terragrunt run-all output

# Validate all module configurations
terragrunt run-all validate

# Initialize all modules
terragrunt run-all init

# Refresh state for all modules
terragrunt run-all refresh
```

### Useful OCI CLI Commands

```bash
# List regions
oci iam region list

# List compartments
oci iam compartment list

# List availability domains
oci iam availability-domain list --compartment-id <compartment-ocid>

# Check current configuration
oci iam user get --user-id <user-ocid>
```

## 🔍 Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify OCI CLI configuration: `oci iam user get --user-id <your-user-ocid>`
   - Check private key permissions: `chmod 600 ~/.oci/oci_api_key.pem`
   - Verify fingerprint matches your public key

2. **Permission Errors**:
   - Ensure your user has necessary IAM policies
   - Check compartment access permissions

3. **Network Errors**:
   - Verify CIDR blocks don't overlap with existing networks
   - Check availability domain limits

4. **State Lock Issues**:
   - Use `terragrunt force-unlock <lock-id>` if needed
   - Ensure Object Storage bucket exists and is accessible

### Debug Mode

Enable debug logging:
```bash
export TF_LOG=DEBUG
export TERRAGRUNT_LOG_LEVEL=debug

# Then run your terragrunt commands
cd infrastructure/network
terragrunt plan
```

### Clean Up Cache

If you encounter issues, clean the Terragrunt cache:
```bash
# Remove all .terragrunt-cache directories
find . -type d -name ".terragrunt-cache" -exec rm -rf {} + 2>/dev/null || true

# Remove Terraform lock files
find . -name ".terraform.lock.hcl" -delete 2>/dev/null || true
```

## 🔐 Security Best Practices

1. **Never commit sensitive data** to version control
2. **Use OCI Vault** for secrets management in production
3. **Restrict SSH access** to specific IP ranges
4. **Enable OCI Cloud Guard** for security monitoring
5. **Use separate compartments** for different environments
6. **Regularly rotate API keys** and access credentials

## 📚 Additional Resources

- [OCI Terraform Provider Documentation](https://registry.terraform.io/providers/oracle/oci/latest/docs)
- [Terragrunt Documentation](https://terragrunt.gruntwork.io/docs/)
- [OCI CLI Documentation](https://docs.oracle.com/en-us/iaas/tools/oci-cli/3.22.2/oci_cli_docs/)
- [OCI Architecture Center](https://docs.oracle.com/solutions/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 🎯 Next Steps After Deployment

### After Network Module Deployment

1. **Verify Network Resources**:
   ```bash
   cd infrastructure/network
   terragrunt output
   cd ../..
   ```

2. **Test Connectivity**:
   - Create a test compute instance in public subnet
   - Verify internet connectivity from public subnet
   - Test NAT gateway from private subnet

### Complete Deployment Workflow

Here's the complete step-by-step deployment process:

```bash
# 1. Install prerequisites
./install.sh

# 2. Configure your environment
cp common-vars.yaml.example common-vars.yaml
# Edit common-vars.yaml with your OCI details

# 3. Validate configuration
oci iam user get --user-id $(oci iam user list --query 'data[0].id' --raw-output)

# 4. Deploy network (foundation)
cd infrastructure/network
terragrunt init
terragrunt plan
terragrunt apply
cd ../..

# 5. Deploy PostgreSQL (when implemented)
cd infrastructure/postgresql
terragrunt init
terragrunt plan
terragrunt apply
cd ../..

# 6. Deploy Kubernetes (when implemented)
cd infrastructure/kubernetes
terragrunt init
terragrunt plan
terragrunt apply
cd ../..

# 7. View all outputs
cd infrastructure/network && terragrunt output && cd ../..
cd infrastructure/postgresql && terragrunt output && cd ../..
cd infrastructure/kubernetes && terragrunt output && cd ../..
```

### Alternative: Deploy All at Once

```bash
# Deploy all modules together (after configuration)
terragrunt run-all init
terragrunt run-all plan
terragrunt run-all apply
```

### Implementing PostgreSQL Module

The PostgreSQL module is currently a placeholder. To implement:

1. **Update `modules/postgresql/main.tf`** with OCI Database System resources
2. **Configure database parameters** in `infrastructure/postgresql/terragrunt.hcl`
3. **Set secure passwords** using OCI Vault or environment variables

Example implementation snippet:
```hcl
resource "oci_database_db_system" "postgresql" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_ocid
  # ... additional configuration
}
```

### Implementing Kubernetes Module

The Kubernetes module is currently a placeholder. To implement:

1. **Update `modules/kubernetes/main.tf`** with OKE cluster resources
2. **Configure node pools** and cluster settings
3. **Set up kubectl access** after deployment

Example implementation snippet:
```hcl
resource "oci_containerengine_cluster" "oke_cluster" {
  compartment_id     = var.compartment_ocid
  kubernetes_version = var.kubernetes_version
  name               = "${var.project_name}-${var.environment}-oke"
  vcn_id             = var.vcn_id
  # ... additional configuration
}
```

## 🔧 Advanced Configuration

### Custom Port Configuration

To add custom ports, update `common-vars.yaml`:

```yaml
security_rules:
  custom:
    - port: 3000
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Custom application port"
```

### Multi-Region Setup

For multi-region deployment:

1. **Create separate directories** for each region
2. **Update region-specific variables** in each configuration
3. **Use different state buckets** for each region

### Environment-Specific Overrides

While this setup uses a single environment, you can create environment-specific overrides:

```hcl
# In infrastructure/network/terragrunt.hcl
inputs = {
  # Override default CIDR for this deployment
  vcn_cidr = "**********/16"

  # Custom security rules
  security_rules = {
    # ... custom rules
  }
}
```

## 📊 Monitoring and Maintenance

### Resource Monitoring

1. **Enable OCI Monitoring** for all resources
2. **Set up alerts** for critical metrics
3. **Use OCI Logging** for audit trails

### Regular Maintenance

1. **Update Terraform/Terragrunt** versions regularly
2. **Review and rotate** API keys quarterly
3. **Audit security groups** and access rules
4. **Monitor costs** using OCI Cost Management

### Backup Strategy

1. **Terraform State**: Stored in OCI Object Storage with versioning
2. **Database Backups**: Automated through OCI Database Service
3. **Application Data**: Implement backup policies for persistent volumes

## 🚨 Emergency Procedures

### Disaster Recovery

1. **State File Recovery**:
   ```bash
   # List state file versions
   oci os object list --bucket-name oracle-infra-production-terraform-state

   # Restore previous version if needed
   oci os object get --bucket-name oracle-infra-production-terraform-state --name terraform.tfstate
   ```

2. **Infrastructure Recovery**:
   ```bash
   # Re-deploy from scratch
   terragrunt run-all destroy  # Only if necessary
   terragrunt run-all apply
   ```

### Rollback Procedures

1. **Module-level rollback**:
   ```bash
   cd infrastructure/module-name
   terragrunt plan -destroy
   terragrunt destroy
   ```

2. **Configuration rollback**:
   - Revert changes in Git
   - Run `terragrunt plan` to verify changes
   - Apply the reverted configuration

## 📞 Support and Contact

For issues and questions:

1. **Check the troubleshooting section** above
2. **Review OCI documentation** for service-specific issues
3. **Check Terragrunt logs** for configuration problems
4. **Open an issue** in this repository for bugs or feature requests

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

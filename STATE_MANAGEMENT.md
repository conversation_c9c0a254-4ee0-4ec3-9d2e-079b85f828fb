# 🗄️ **Oracle Cloud Infrastructure State Management**

## 📋 **Overview**

This project uses a **hybrid state management approach** that combines local Terraform state with automated backup to Oracle Cloud Object Storage. This provides the benefits of both local state (fast operations) and remote state (backup, versioning, team collaboration).

## 🏗️ **Architecture**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Local State   │    │   State Sync     │    │  OCI Object Storage │
│   (Terragrunt)  │◄──►│     Script       │◄──►│      Bucket         │
│                 │    │                  │    │                     │
│ ✅ Fast ops     │    │ ✅ Automated     │    │ ✅ Versioned        │
│ ✅ No network   │    │ ✅ Backup        │    │ ✅ Team access      │
│ ✅ Simple       │    │ ✅ Restore       │    │ ✅ Audit trail      │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
```

## 🚀 **Quick Start**

### **1. Deploy Infrastructure with Local State**
```bash
# Deploy any module (e.g., network)
cd infrastructure/network
terragrunt init
terragrunt apply

# State is stored locally in .terragrunt-cache/
```

### **2. Backup State to OCI Object Storage**
```bash
# Upload specific module state
./scripts/state-sync.sh upload network "infrastructure/network/.terragrunt-cache/.../terraform.tfstate"

# Or upload all module states
./scripts/state-sync.sh sync-up
```

### **3. List and Manage States**
```bash
# List all state files in OCI bucket
./scripts/state-sync.sh list

# Download specific state file
./scripts/state-sync.sh download network /path/to/local/file.tfstate
```

## 🛠️ **State Sync Script Usage**

### **Commands**

| Command | Description | Example |
|---------|-------------|---------|
| `upload <module> <file>` | Upload specific state file | `./scripts/state-sync.sh upload network state.tfstate` |
| `download <module> <file>` | Download specific state file | `./scripts/state-sync.sh download network state.tfstate` |
| `sync-up` | Upload all module states | `./scripts/state-sync.sh sync-up` |
| `sync-down` | Download all module states | `./scripts/state-sync.sh sync-down` |
| `list` | List all state files in bucket | `./scripts/state-sync.sh list` |

### **Configuration**

The script uses these settings (configurable in `scripts/state-sync.sh`):
- **Bucket**: `oracle-dev-terraform-state`
- **Namespace**: `axpq44vjev4y`
- **Region**: `us-phoenix-1`

## 📁 **State Storage Structure**

```
oracle-dev-terraform-state/
├── network/
│   ├── terraform.tfstate              # Current state
│   └── backups/
│       ├── terraform.tfstate.20250703_100826
│       └── terraform.tfstate.20250703_101234
├── postgresql/
│   ├── terraform.tfstate
│   └── backups/
│       └── terraform.tfstate.20250703_102345
├── kubernetes/
│   ├── terraform.tfstate
│   └── backups/
│       └── terraform.tfstate.20250703_103456
└── object-storage/
    ├── terraform.tfstate
    └── backups/
        └── terraform.tfstate.20250703_104567
```

## 🔄 **Workflow Examples**

### **Daily Development Workflow**
```bash
# 1. Work with local state (fast)
cd infrastructure/network
terragrunt plan
terragrunt apply

# 2. Backup to OCI after major changes
cd ../..
./scripts/state-sync.sh upload network "infrastructure/network/.terragrunt-cache/.../terraform.tfstate"
```

### **Team Collaboration Workflow**
```bash
# 1. Download latest state from team
./scripts/state-sync.sh download network infrastructure/network/terraform.tfstate

# 2. Work on changes
cd infrastructure/network
terragrunt plan
terragrunt apply

# 3. Upload updated state
cd ../..
./scripts/state-sync.sh upload network "infrastructure/network/.terragrunt-cache/.../terraform.tfstate"
```

### **Disaster Recovery Workflow**
```bash
# 1. List available backups
./scripts/state-sync.sh list

# 2. Download specific backup
oci os object get \
  --bucket-name oracle-dev-terraform-state \
  --namespace axpq44vjev4y \
  --name "network/backups/terraform.tfstate.20250703_100826" \
  --file recovered-state.tfstate

# 3. Restore to module
cp recovered-state.tfstate infrastructure/network/terraform.tfstate
```

## ⚙️ **Advanced Configuration**

### **Environment-Specific Buckets**
To use different buckets for different environments, update `common-vars.yaml`:

```yaml
object_storage:
  bucket_name: "oracle-${environment.name}-terraform-state"  # oracle-dev, oracle-prod, etc.
```

### **Automated State Sync**
Add to your CI/CD pipeline:

```bash
# After successful deployment
./scripts/state-sync.sh sync-up

# Before deployment (to get latest state)
./scripts/state-sync.sh sync-down
```

### **State Locking (Future Enhancement)**
For true remote state with locking, consider:
1. **DynamoDB-style locking** using OCI Database
2. **HTTP backend** with custom locking service
3. **Git-based state** with merge conflict resolution

## 🔐 **Security Best Practices**

1. **Bucket Access**: Ensure only authorized users can access the state bucket
2. **Encryption**: State files are stored with OCI Object Storage encryption
3. **Versioning**: Enabled on the bucket for state history
4. **Backup Retention**: Automatic timestamped backups for recovery

## 🚨 **Troubleshooting**

### **Common Issues**

| Issue | Solution |
|-------|----------|
| "Bucket not found" | Verify bucket name and OCI CLI configuration |
| "Permission denied" | Check OCI IAM policies for Object Storage access |
| "State file not found" | Use `find` to locate actual state file in `.terragrunt-cache` |
| "Upload failed" | Check network connectivity and OCI credentials |

### **State File Locations**
```bash
# Find actual state files
find infrastructure -name "terraform.tfstate" -type f

# Typical location
infrastructure/MODULE/.terragrunt-cache/HASH/HASH/terraform.tfstate
```

## 📊 **Current Status**

✅ **Deployed and Working:**
- Object Storage bucket: `oracle-dev-terraform-state`
- Network module state: Successfully uploaded and verified
- State sync script: Fully functional

✅ **Tested Operations:**
- Upload state file to OCI Object Storage
- Download state file from OCI Object Storage
- List state files in bucket
- Automatic backup creation with timestamps

🎯 **Next Steps:**
1. Deploy additional modules (PostgreSQL, Kubernetes)
2. Test state sync with multiple modules
3. Set up automated state sync in CI/CD pipeline
4. Consider implementing state locking for team environments

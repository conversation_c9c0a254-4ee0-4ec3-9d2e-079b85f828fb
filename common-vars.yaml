# Common variables for all Terragrunt modules
# This file contains shared configuration that will be used across all modules

# Oracle Cloud Infrastructure Configuration
oci:
  # OCI Region (e.g., us-ashburn-1, us-phoenix-1, eu-frankfurt-1)
  region: "us-phoenix-1"
  
  # OCI Tenancy OCID - Replace with your tenancy OCID
  tenancy_ocid: "ocid1.tenancy.oc1..aaaaaaaabfgtl4uqqutqesytp5hi37l54nde4dtrlg2voystwjcfw5p6lama"
  
  # OCI User OCID - Replace with your user OCID
  user_ocid: "ocid1.user.oc1..aaaaaaaayly7dsyreiud2zvzxx3fpr4lhjmkg2yglf4kspe4n7u677nu7hdq"
  
  # OCI Compartment OCID - Replace with your compartment OCID
  compartment_ocid: "ocid1.tenancy.oc1..aaaaaaaabfgtl4uqqutqesytp5hi37l54nde4dtrlg2voystwjcfw5p6lama"
  
  # Path to your OCI private key file
  private_key_path: "~/.oci/oci_api_key.pem"
  
  # Fingerprint of your OCI public key
  fingerprint: "67:13:16:63:6a:ac:05:d9:ce:bb:9e:72:97:9d:90:3f:ae:57:88:26"

# Environment Configuration
environment:
  name: "oracle-dev"
  project_name: "oracle-dev"
  
# Network Configuration
network:
  # VCN CIDR block
  vcn_cidr: "10.0.0.0/16"
  
  # Public subnet CIDRs (3 subnets across different ADs)
  public_subnets:
    - cidr: "10.0.1.0/24"
      name: "public-subnet-1"
      availability_domain: 1
    - cidr: "10.0.2.0/24"
      name: "public-subnet-2"
      availability_domain: 2
    - cidr: "10.0.3.0/24"
      name: "public-subnet-3"
      availability_domain: 3
  
  # Private subnet CIDRs (3 subnets across different ADs)
  private_subnets:
    - cidr: "*********/24"
      name: "private-subnet-1"
      availability_domain: 1
    - cidr: "*********/24"
      name: "private-subnet-2"
      availability_domain: 2
    - cidr: "*********/24"
      name: "private-subnet-3"
      availability_domain: 3

# Security Group Rules - Define allowed ports and protocols
security_rules:
  # Web traffic
  web:
    - port: 80
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTP traffic"
    - port: 443
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTPS traffic"
  
  # SSH access
  ssh:
    - port: 22
      protocol: "TCP"
      source: "0.0.0.0/0"  # Restrict this to your IP range in production
      description: "SSH access"
  
  # Database access (PostgreSQL)
  database:
    - port: 5432
      protocol: "TCP"
      source: "10.0.0.0/16"  # Only from VCN
      description: "PostgreSQL access"
  
  # Kubernetes API
  kubernetes:
    - port: 6443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Kubernetes API server"
    - port: 2379
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "etcd client communication"
    - port: 2380
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "etcd peer communication"
    - port: 10250
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Kubelet API"
    - port: 10251
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "kube-scheduler"
    - port: 10252
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "kube-controller-manager"
  
  # Custom application ports
  application:
    - port: 443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Application port 1"
    - port: 8443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Application port 2"
    - port: 9084
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Monitoring/Prometheus"

# Tags to be applied to all resources
common_tags:
  Environment: "deev"
  Project: "oracle-dev"
  ManagedBy: "Terragrunt"
  Owner: "DevOps Team"

# PostgreSQL Configuration (for future module)
postgresql:
  # Database configuration will be added when implementing the module
  placeholder: true

# Kubernetes Configuration (for future module)
kubernetes:
  # Kubernetes cluster configuration will be added when implementing the module
  placeholder: true

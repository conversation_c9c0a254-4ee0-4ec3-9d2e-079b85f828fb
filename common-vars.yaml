# Common variables for all Terragrunt modules
# This file contains shared configuration that will be used across all modules

# Oracle Cloud Infrastructure Configuration
oci:
  # OCI Region (e.g., us-ashburn-1, us-phoenix-1, eu-frankfurt-1)
  region: "us-phoenix-1"
  
  # OCI Tenancy OCID - Replace with your tenancy OCID
  tenancy_ocid: "ocid1.tenancy.oc1..aaaaaaaabfgtl4uqqutqesytp5hi37l54nde4dtrlg2voystwjcfw5p6lama"
  
  # OCI User OCID - Replace with your user OCID
  user_ocid: "ocid1.user.oc1..aaaaaaaayly7dsyreiud2zvzxx3fpr4lhjmkg2yglf4kspe4n7u677nu7hdq"
  
  # OCI Compartment OCID - Replace with your compartment OCID
  compartment_ocid: "ocid1.tenancy.oc1..aaaaaaaabfgtl4uqqutqesytp5hi37l54nde4dtrlg2voystwjcfw5p6lama"
  
  # Path to your OCI private key file
  private_key_path: "~/.oci/oci_api_key.pem"
  
  # Fingerprint of your OCI public key
  fingerprint: "b3:27:04:5b:bb:fe:45:ac:21:1e:b4:b6:30:e8:51:ac"

# Environment Configuration
environment:
  project_name: "oracle"
  name: "dev"
  
  
# Network Configuration
network:
  # VCN CIDR block
  vcn_cidr: "10.0.0.0/16"
  
  # Public subnet CIDRs (3 subnets across different ADs)
  public_subnets:
    - cidr: "10.0.1.0/24"
      name: "public-subnet-1"
      availability_domain: 1
    - cidr: "10.0.2.0/24"
      name: "public-subnet-2"
      availability_domain: 2
    - cidr: "10.0.3.0/24"
      name: "public-subnet-3"
      availability_domain: 3
  
  # Private subnet CIDRs (3 subnets across different ADs)
  private_subnets:
    - cidr: "*********/24"
      name: "private-subnet-1"
      availability_domain: 1
    - cidr: "*********/24"
      name: "private-subnet-2"
      availability_domain: 2
    - cidr: "*********/24"
      name: "private-subnet-3"
      availability_domain: 3

# Security Group Rules - Define allowed ports and protocols
security_rules:
  # Web traffic
  web:
    - port: 80
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTP traffic"
    - port: 443
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTPS traffic"
  
  # SSH access
  ssh:
    - port: 22
      protocol: "TCP"
      source: "0.0.0.0/0"  # Restrict this to your IP range in production
      description: "SSH access"
  
  # Database access (PostgreSQL)
  database:
    - port: 5432
      protocol: "TCP"
      source: "10.0.0.0/16"  # Only from VCN
      description: "PostgreSQL access"
  
  # Kubernetes API
  kubernetes:
    - port: 6443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Kubernetes API server"
    - port: 2379
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "etcd client communication"
    - port: 2380
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "etcd peer communication"
    - port: 10250
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Kubelet API"
    - port: 10251
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "kube-scheduler"
    - port: 10252
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "kube-controller-manager"
  
  # Custom application ports
  application:
    - port: 443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Application port 1"
    - port: 8443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Application port 2"
    - port: 9084
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Monitoring/Prometheus"

# Tags to be applied to all resources
common_tags:
  Environment: "dev"
  Project: "oracle-dev"
  ManagedBy: "Terragrunt"
  Owner: "DevOps Team"

# PostgreSQL Configuration
postgresql:
  # Database instance configuration
  instance_shape: "VM.Standard2.1"
  storage_size_gb: 256
  backup_retention_days: 7
  enable_backup: true
  backup_window_start_time: "02:00"
  high_availability: false
  monitoring_enabled: true
  ssh_public_key: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDl0rRXwBdFLRiSFmkEMC/35qo87HjndCDlCX+6ZpYywSmqBG7fmA5lJsLg4h79Vf8EmxWqY/0qKSduzsJ0n5cIhPkF7GO+hP3oRJAkhhFMPRvPnBJmct1f5OpCCJGSny/wYTb4qCTY1SQIbMkhRfYYzMbVJGziL3sCzTbnMclVphJ7+POVMXD/iRdat1o9lb23AtTwEzMKPqrL0oio+EqLYrcYvX1jBWqCn7qiPVKOtOMtu0CFjM2xev7Z+n4Cyg6fOggDYCJ+FNP91pLgtVYWpeAlhJBJwmzlsTzf0yUkjefjbc/IPs19Pvo/awaCkYaUruBY6owS4lVZuYw6mDBn <EMAIL>"  # Add your SSH public key here
  db_password: "Snapstore32!"  # Generate a random password if empty

# Kubernetes Configuration
kubernetes:
  # Cluster configuration
  kubernetes_version: "v1.33.1"
  cluster_name: ""  # Will use default naming if empty

  # API endpoint configuration
  api_access_cidr: "0.0.0.0/0"  # Restrict this in production
  enable_monitoring: true

  # Multiple node pools configuration
  node_pools:
    # Default worker node pool
    - name: "workers"
      node_shape: "VM.Standard.E3.Flex"
      node_shape_memory_gb: 16
      node_shape_ocpus: 2
      initial_node_labels:
        environment: "dev"
        managed-by: "terragrunt"
        pool-type: "workers"
      size: 1
      max_size: 10
      min_size: 1
      boot_volume_size_gb: 50
      image_id: "ocid1.image.oc1.phx.aaaaaaaa6fbid3anokvq3rjxt22globuvoi4j6adgbyroehadb4tc4tb6b2a"  # Oracle Linux 8 x86_64 for OKE 1.33.1

  # SSH key for worker nodes
  ssh_public_key: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDl0rRXwBdFLRiSFmkEMC/35qo87HjndCDlCX+6ZpYywSmqBG7fmA5lJsLg4h79Vf8EmxWqY/0qKSduzsJ0n5cIhPkF7GO+hP3oRJAkhhFMPRvPnBJmct1f5OpCCJGSny/wYTb4qCTY1SQIbMkhRfYYzMbVJGziL3sCzTbnMclVphJ7+POVMXD/iRdat1o9lb23AtTwEzMKPqrL0oio+EqLYrcYvX1jBWqCn7qiPVKOtOMtu0CFjM2xev7Z+n4Cyg6fOggDYCJ+FNP91pLgtVYWpeAlhJBJwmzlsTzf0yUkjefjbc/IPs19Pvo/awaCkYaUruBY6owS4lVZuYw6mDBn <EMAIL>"  # Add your SSH public key here

  # Cluster autoscaler configuration
  autoscaler:
    enabled: true
    namespace: "kube-system"
    image: "k8s.gcr.io/autoscaling/cluster-autoscaler:v1.33.1"
    min_nodes: 1
    max_nodes: 10
    max_node_provision_time: "15m"
    scale_down_delay_after_add: "10m"
    scale_down_unneeded_time: "10m"
    scale_down_util_threshold: "0.5"
    skip_nodes_with_local_storage: false
    skip_nodes_with_system_pods: true
    resources:
      limits:
        cpu: "100m"
        memory: "300Mi"
      requests:
        cpu: "100m"
        memory: "300Mi"

# Load Balancer Configuration
load_balancer:
  # Load balancer shape and bandwidth
  shape: "flexible"
  shape_details:
    maximum_bandwidth_in_mbps: 100
    minimum_bandwidth_in_mbps: 10
  is_private: false

  # Backend sets configuration
  backend_sets:
    web-backend:
      policy: "ROUND_ROBIN"
      health_checker:
        protocol: "HTTP"
        port: 80
        url_path: "/health"
        return_code: 200
        interval_ms: 10000
        timeout_in_millis: 3000
        retries: 3
      backends: []  # Add your backend servers here
      session_persistence_enabled: false

    # API backend (optional)
    api-backend:
      enabled: false  # Set to true to enable API backend
      policy: "ROUND_ROBIN"
      health_checker:
        protocol: "HTTP"
        port: 8080
        url_path: "/api/health"
        return_code: 200
        interval_ms: 10000
        timeout_in_millis: 3000
        retries: 3
      backends: []
      session_persistence_enabled: false

  # Listeners configuration
  listeners:
    http-listener:
      port: 80
      protocol: "HTTP"
      default_backend_set_name: "web-backend"
      connection_configuration:
        idle_timeout_in_seconds: 300

    # HTTPS listener (optional)
    https-listener:
      enabled: false  # Set to true to enable HTTPS
      port: 443
      protocol: "HTTP"
      default_backend_set_name: "web-backend"
      ssl_configuration:
        certificate_name: "ssl-cert"
        verify_depth: 5
        verify_peer_certificate: false
      connection_configuration:
        idle_timeout_in_seconds: 300

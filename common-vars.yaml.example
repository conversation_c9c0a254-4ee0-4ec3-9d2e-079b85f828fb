# Example Common Variables Configuration
# Copy this file to common-vars.yaml and update with your actual values

# Oracle Cloud Infrastructure Configuration
oci:
  # OCI Region (e.g., us-ashburn-1, us-phoenix-1, eu-frankfurt-1)
  region: "us-ashburn-1"
  
  # OCI Tenancy OCID - Get from OCI Console -> Administration -> Tenancy Details
  tenancy_ocid: "ocid1.tenancy.oc1..aaaaaaaa..."
  
  # OCI User OCID - Get from OCI Console -> Identity -> Users
  user_ocid: "ocid1.user.oc1..aaaaaaaa..."
  
  # OCI Compartment OCID - Get from OCI Console -> Identity -> Compartments
  compartment_ocid: "ocid1.compartment.oc1..aaaaaaaa..."
  
  # Path to your OCI private key file (generated during API key setup)
  private_key_path: "~/.oci/oci_api_key.pem"
  
  # Fingerprint of your OCI public key (shown when you upload the public key)
  fingerprint: "aa:bb:cc:dd:ee:ff:00:11:22:33:44:55:66:77:88:99"

# Environment Configuration
environment:
  name: "production"  # Single environment name
  project_name: "oracle-infra"  # Your project name (used in resource naming)
  
# Network Configuration
network:
  # VCN CIDR block - adjust if conflicts with existing networks
  vcn_cidr: "10.0.0.0/16"
  
  # Public subnet CIDRs (3 subnets across different ADs)
  public_subnets:
    - cidr: "10.0.1.0/24"
      name: "public-subnet-1"
      availability_domain: 1
    - cidr: "10.0.2.0/24"
      name: "public-subnet-2"
      availability_domain: 2
    - cidr: "10.0.3.0/24"
      name: "public-subnet-3"
      availability_domain: 3
  
  # Private subnet CIDRs (3 subnets across different ADs)
  private_subnets:
    - cidr: "*********/24"
      name: "private-subnet-1"
      availability_domain: 1
    - cidr: "*********/24"
      name: "private-subnet-2"
      availability_domain: 2
    - cidr: "*********/24"
      name: "private-subnet-3"
      availability_domain: 3

# Security Group Rules - Define allowed ports and protocols
security_rules:
  # Web traffic (accessible from internet)
  web:
    - port: 80
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTP traffic"
    - port: 443
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTPS traffic"
  
  # SSH access (restrict source IP in production!)
  ssh:
    - port: 22
      protocol: "TCP"
      source: "0.0.0.0/0"  # CHANGE THIS: Use your office IP range like "***********/24"
      description: "SSH access"
  
  # Database access (PostgreSQL) - only from within VCN
  database:
    - port: 5432
      protocol: "TCP"
      source: "10.0.0.0/16"  # Only from VCN
      description: "PostgreSQL access"
  
  # Kubernetes API and related ports - only from within VCN
  kubernetes:
    - port: 6443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Kubernetes API server"
    - port: 2379
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "etcd client communication"
    - port: 2380
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "etcd peer communication"
    - port: 10250
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Kubelet API"
    - port: 10251
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "kube-scheduler"
    - port: 10252
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "kube-controller-manager"
  
  # Custom application ports - adjust as needed
  application:
    - port: 8080
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Application port 1"
    - port: 8443
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Application port 2"
    - port: 9090
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "Monitoring/Prometheus"

# Tags to be applied to all resources
common_tags:
  Environment: "production"
  Project: "oracle-infra"
  ManagedBy: "Terragrunt"
  Owner: "DevOps Team"
  # Add more tags as needed:
  # CostCenter: "IT"
  # Department: "Engineering"

# PostgreSQL Configuration (for future module implementation)
postgresql:
  # Database configuration will be added when implementing the module
  placeholder: true

# Kubernetes Configuration (for future module implementation)
kubernetes:
  # Kubernetes cluster configuration will be added when implementing the module
  placeholder: true

# Additional Notes:
# 1. Never commit the actual common-vars.yaml file with real credentials to version control
# 2. Use OCI Vault for storing sensitive information in production
# 3. Regularly rotate API keys and update fingerprints
# 4. Adjust CIDR blocks to avoid conflicts with existing networks
# 5. Restrict SSH access to specific IP ranges in production environments

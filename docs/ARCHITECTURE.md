# 🏗️ Architecture Overview

## 🎯 Infrastructure Components

### **Core Infrastructure**
```
Oracle Cloud Infrastructure (us-phoenix-1)
├── 📦 Object Storage
│   └── oracle-dev-terraform-state (State Management)
│
├── 🌐 Network Infrastructure
│   ├── VCN: oracle-dev-vcn (10.0.0.0/16)
│   ├── Public Subnets: 3 ADs (Load Balancers, Bastion)
│   ├── Private Subnets: 3 ADs (Applications, Databases)
│   ├── OKE Regional Subnet: API server access
│   └── Security Lists: Comprehensive port configuration
│
├── 🗄️ PostgreSQL Database
│   ├── Instance: PostgreSQL.VM.Standard.E4.Flex.2.32GB
│   ├── Storage: 256GB with automated backups
│   └── High Availability: Configurable
│
└── ☸️ Kubernetes Cluster (OKE)
    ├── Control Plane: oracle-dev-oke (v1.33.1)
    ├── Worker Pool: oracle-dev-workers
    ├── Node Shape: VM.Standard.E3.Flex (2 OCPUs, 8GB)
    └── Auto-scaling: 1-10 nodes
```

## 🌐 Network Architecture

### **VCN Design**
- **CIDR Block**: 10.0.0.0/16 (65,536 IP addresses)
- **Availability Domains**: 3 (High Availability)
- **Subnet Strategy**: Public/Private separation

### **Subnet Layout**
```
Public Subnets (Internet-facing):
├── AD-1: ********/24  (Load Balancers, NAT Gateway)
├── AD-2: ********/24  (Load Balancers, Bastion)
└── AD-3: ********/24  (Load Balancers, Public Services)

Private Subnets (Internal):
├── AD-1: *********/24 (Kubernetes Workers, Applications)
├── AD-2: *********/24 (Kubernetes Workers, Applications)
└── AD-3: *********/24 (Kubernetes Workers, Applications)

OKE Regional Subnet:
└── Regional: *********/24 (Kubernetes API Server)
```

### **Security Groups**
- **Kubernetes API**: Port 6443 (Public access)
- **Worker Communication**: Port 12250 (Internal)
- **NodePort Services**: 30000-32767 (Load balancer access)
- **Health Checks**: Port 10256 (kube-proxy)

## ☸️ Kubernetes Architecture

### **Cluster Components**
```
OKE Cluster (oracle-dev-oke)
├── 🎛️ Control Plane (Managed by Oracle)
│   ├── API Server: 137.131.29.18:6443
│   ├── etcd: Managed storage
│   ├── Scheduler: Pod placement
│   └── Controller Manager: Resource management
│
├── 👥 Worker Nodes (oracle-dev-workers)
│   ├── Node Count: 1 (auto-scaling 1-10)
│   ├── Instance Type: VM.Standard.E3.Flex
│   ├── Resources: 2 OCPUs, 8GB RAM, 50GB storage
│   └── Placement: Across all 3 ADs
│
└── 🔧 System Components
    ├── CoreDNS: Cluster DNS resolution
    ├── kube-proxy: Network proxy and load balancing
    ├── Flannel: Pod networking (CNI)
    └── CSI Driver: OCI storage integration
```

### **Networking Model**
- **Pod CIDR**: 10.244.0.0/16 (Pod-to-pod communication)
- **Service CIDR**: 10.96.0.0/16 (Service discovery)
- **CNI**: Flannel for overlay networking
- **Load Balancing**: OCI Load Balancer integration

## 🗄️ Database Architecture

### **PostgreSQL Configuration**
```
PostgreSQL Database System
├── 🖥️ Instance Configuration
│   ├── Shape: PostgreSQL.VM.Standard.E4.Flex.2.32GB
│   ├── CPU: 2 OCPUs
│   ├── Memory: 32GB RAM
│   └── Storage: 256GB (expandable)
│
├── 🔒 Security
│   ├── Network: Private subnet only
│   ├── Access: VCN internal (10.0.0.0/16)
│   └── Port: 5432 (restricted)
│
├── 💾 Backup & Recovery
│   ├── Automated Backups: Daily
│   ├── Retention: 7 days
│   ├── Backup Window: 02:00 UTC
│   └── Point-in-time Recovery: Available
│
└── 📊 Monitoring
    ├── OCI Monitoring: Enabled
    ├── Performance Insights: Available
    └── Alerting: Configurable
```

## 📦 State Management Architecture

### **Terraform State Storage**
```
OCI Object Storage (oracle-dev-terraform-state)
├── 📁 Module Organization
│   ├── network/terraform.tfstate
│   ├── postgresql/terraform.tfstate
│   ├── kubernetes/terraform.tfstate
│   └── object-storage/terraform.tfstate
│
├── 🔄 Backup Strategy
│   ├── Timestamped Backups: Automatic
│   ├── Version Control: Object versioning
│   └── Recovery: Point-in-time restore
│
└── 🔐 Security
    ├── Encryption: At rest and in transit
    ├── Access Control: OCI IAM
    └── Audit Logging: Enabled
```

## 🔐 Security Architecture

### **Network Security**
- **Defense in Depth**: Multiple security layers
- **Principle of Least Privilege**: Minimal required access
- **Network Segmentation**: Public/private subnet isolation

### **Access Control**
```
Security Model
├── 🌐 Network Level
│   ├── Security Lists: Port-based filtering
│   ├── Network Security Groups: Application-specific rules
│   └── Route Tables: Traffic flow control
│
├── 🔑 Identity & Access
│   ├── OCI IAM: User and service authentication
│   ├── Kubernetes RBAC: Cluster access control
│   └── SSH Keys: Instance access
│
└── 🛡️ Data Protection
    ├── Encryption at Rest: All storage encrypted
    ├── Encryption in Transit: TLS/SSL everywhere
    └── Backup Encryption: Automated
```

## 📊 Monitoring & Observability

### **Monitoring Stack**
```
Observability
├── 📈 OCI Monitoring
│   ├── Infrastructure Metrics: CPU, Memory, Network
│   ├── Database Metrics: Performance, Connections
│   └── Kubernetes Metrics: Cluster health, Pod status
│
├── 📋 Logging
│   ├── OCI Logging: Centralized log collection
│   ├── Audit Logs: Security and compliance
│   └── Application Logs: Container stdout/stderr
│
└── 🚨 Alerting
    ├── Threshold Alerts: Resource utilization
    ├── Health Checks: Service availability
    └── Notification Channels: Email, Slack, PagerDuty
```

## 🔄 Deployment Architecture

### **Infrastructure as Code**
```
IaC Pipeline
├── 📝 Configuration
│   ├── common-vars.yaml: Centralized variables
│   ├── root.hcl: Terragrunt root configuration
│   └── Module Configs: terragrunt.hcl per module
│
├── 🏗️ Modules
│   ├── Terraform Modules: Reusable infrastructure components
│   ├── Dependency Management: Automatic ordering
│   └── State Management: Isolated per module
│
└── 🚀 Deployment
    ├── Local Development: Fast iteration
    ├── State Backup: Automatic to OCI Object Storage
    └── Team Collaboration: Shared state access
```

## 🎯 Design Principles

### **Scalability**
- **Horizontal Scaling**: Auto-scaling node pools
- **Vertical Scaling**: Flexible instance shapes
- **Storage Scaling**: Expandable database storage

### **Reliability**
- **Multi-AZ Deployment**: High availability across zones
- **Automated Backups**: Data protection and recovery
- **Health Monitoring**: Proactive issue detection

### **Security**
- **Zero Trust**: Verify everything, trust nothing
- **Encryption Everywhere**: Data protection at all layers
- **Minimal Attack Surface**: Least privilege access

### **Cost Optimization**
- **Right-sizing**: Appropriate resource allocation
- **Auto-scaling**: Pay for what you use
- **Reserved Capacity**: Long-term cost savings

## 🚀 Scalability Considerations

### **Growth Patterns**
- **Kubernetes Nodes**: 1 → 10 nodes (auto-scaling)
- **Database**: Vertical scaling to larger shapes
- **Storage**: Automatic expansion as needed
- **Network**: Additional subnets for new services

### **Performance Optimization**
- **Instance Placement**: Spread across ADs
- **Network Optimization**: Regional subnets for low latency
- **Storage Performance**: High-performance block storage
- **Database Tuning**: Optimized PostgreSQL configuration

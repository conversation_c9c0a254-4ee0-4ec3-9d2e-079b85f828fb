# 📋 Commands Reference

## 🚀 Infrastructure Management

### **Deploy Infrastructure**

```bash
# Deploy all modules at once
./scripts/infra-manager.sh deploy

# Deploy with auto-approval (no prompts)
./scripts/infra-manager.sh deploy --auto-approve

# Deploy modules sequentially (safer)
./scripts/infra-manager.sh deploy-sequential

# Plan all changes first
./scripts/infra-manager.sh plan
```

### **Destroy Infrastructure**

```bash
# Destroy all resources (DANGEROUS!)
./scripts/infra-manager.sh destroy

# Destroy with auto-approval (VERY DANGEROUS!)
./scripts/infra-manager.sh destroy --auto-approve

# Destroy modules sequentially (safer)
./scripts/infra-manager.sh destroy-sequential
```

### **Check Status**

```bash
# Show deployment status
./scripts/infra-manager.sh status

# Show deployment summary
./scripts/infra-manager.sh summary
```

## ☸️ Kubernetes Management

### **Cluster Access**

```bash
# Complete setup: kubectl + cluster-autoscaler (if enabled)
./scripts/kube-context.sh full-setup

# Individual commands:
# Configure kubectl for the cluster
./scripts/kube-context.sh update

# Set kubectl context
./scripts/kube-context.sh set

# Test cluster connectivity
./scripts/kube-context.sh test

# Show cluster information
./scripts/kube-context.sh info

# Deploy cluster-autoscaler (if enabled in config)
./scripts/kube-context.sh deploy-autoscaler

# Check cluster-autoscaler status
./scripts/kube-context.sh autoscaler-status

# Backup current kubeconfig
./scripts/kube-context.sh backup
```

### **Basic kubectl Commands**

```bash
# Cluster information
kubectl cluster-info
kubectl get nodes
kubectl get nodes -o wide

# System components
kubectl get pods -A
kubectl get pods -n kube-system
kubectl get services -A

# Resource monitoring
kubectl top nodes
kubectl top pods -A
```

## 📦 State Management

### **Upload States**

```bash
# Upload all module states to OCI Object Storage
./scripts/state-sync.sh sync-up

# Upload specific module state
./scripts/state-sync.sh upload <module> <state-file>

# Example: Upload network state
./scripts/state-sync.sh upload network infrastructure/network/terraform.tfstate
```

### **Download States**

```bash
# Download all module states from OCI Object Storage
./scripts/state-sync.sh sync-down

# Download specific module state
./scripts/state-sync.sh download <module> <state-file>

# Example: Download kubernetes state
./scripts/state-sync.sh download kubernetes infrastructure/kubernetes/terraform.tfstate
```

### **List States**

```bash
# List all state files in OCI Object Storage
./scripts/state-sync.sh list
```

## 🏗️ Terragrunt Commands

### **Single Module Operations**

```bash
# Navigate to module directory
cd infrastructure/network  # or postgresql, kubernetes, object-storage

# Validate configuration
terragrunt validate

# Plan changes
terragrunt plan

# Apply changes
terragrunt apply

# Apply with auto-approval
terragrunt apply --auto-approve

# Show outputs
terragrunt output

# Destroy resources
terragrunt destroy

# Return to root
cd ../..
```

### **All Modules Operations**

```bash
# Run from project root directory

# Validate all configurations
terragrunt run-all validate

# Plan all modules
terragrunt run-all plan

# Apply all modules
terragrunt run-all apply

# Apply all with auto-approval
terragrunt run-all apply --terragrunt-non-interactive

# Show all outputs
terragrunt run-all output

# Destroy all modules (DANGEROUS!)
terragrunt run-all destroy

# Destroy all with auto-approval (VERY DANGEROUS!)
terragrunt run-all destroy --terragrunt-non-interactive
```

## 🔧 Setup & Installation

### **Initial Setup**

```bash
# Install all dependencies
./scripts/install.sh

# Configure OCI CLI
oci setup config

# Test OCI configuration
oci iam user get --user-id $(oci iam user list --query 'data[0].id' --raw-output)
```

### **Configuration**

```bash
# Copy configuration template
cp common-vars.yaml.example common-vars.yaml

# Edit configuration
nano common-vars.yaml
# or
vim common-vars.yaml
```

## 📊 Monitoring & Debugging

### **Infrastructure Status**

```bash
# Check Terraform state
cd infrastructure/<module>
terragrunt show
terragrunt state list
terragrunt state show <resource>

# Check for drift
terragrunt plan -detailed-exitcode
```

### **Kubernetes Debugging**

```bash
# Check cluster health
kubectl get componentstatuses
kubectl get events --sort-by=.metadata.creationTimestamp

# Debug pods
kubectl describe pod <pod-name>
kubectl logs <pod-name>
kubectl logs -f <pod-name>  # Follow logs

# Debug services
kubectl describe service <service-name>
kubectl get endpoints <service-name>

# Debug nodes
kubectl describe node <node-name>
kubectl get nodes -o yaml
```

### **Network Debugging**

```bash
# Test connectivity from pod
kubectl run test-pod --image=busybox --rm -it -- sh

# Inside the test pod:
nslookup kubernetes.default
wget -qO- <service-name>.<namespace>.svc.cluster.local
ping <pod-ip>
```

## 🔐 Security Commands

### **SSH Access**

```bash
# Generate SSH key pair
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Copy public key
cat ~/.ssh/id_rsa.pub

# SSH to compute instance (if bastion host is deployed)
ssh -i ~/.ssh/id_rsa opc@<instance-ip>
```

### **OCI Authentication**

```bash
# Setup OCI CLI configuration
oci setup config

# Test authentication
oci iam user get --user-id <user-ocid>

# List compartments
oci iam compartment list

# Get tenancy information
oci iam tenancy get --tenancy-id <tenancy-ocid>
```

## 📈 Scaling Commands

### **Kubernetes Scaling**

```bash
# Scale deployment
kubectl scale deployment <deployment-name> --replicas=5

# Auto-scale deployment
kubectl autoscale deployment <deployment-name> --cpu-percent=70 --min=2 --max=10

# Check HPA status
kubectl get hpa
kubectl describe hpa <hpa-name>
```

### **Node Pool Scaling**

```bash
# Scale node pool via OCI CLI
oci ce node-pool update --node-pool-id <node-pool-id> --size 3

# Get node pool information
oci ce node-pool get --node-pool-id <node-pool-id>

# List node pools
oci ce node-pool list --cluster-id <cluster-id>
```

## 🔄 Backup & Recovery

### **State Backup**

```bash
# Backup all states
./scripts/state-sync.sh sync-up

# Manual state backup
cp infrastructure/<module>/terraform.tfstate backup/terraform.tfstate.$(date +%Y%m%d_%H%M%S)
```

### **Configuration Backup**

```bash
# Backup Kubernetes resources
kubectl get all -o yaml -n <namespace> > backup-$(date +%Y%m%d).yaml

# Backup specific resources
kubectl get configmaps,secrets -o yaml -n <namespace> > configs-backup.yaml
```

## 🛠️ Troubleshooting Commands

### **Common Issues**

```bash
# Check Terragrunt cache
ls -la infrastructure/<module>/.terragrunt-cache/

# Clear Terragrunt cache
rm -rf infrastructure/<module>/.terragrunt-cache/

# Refresh Terraform state
cd infrastructure/<module>
terragrunt refresh

# Import existing resource
terragrunt import <resource-type>.<resource-name> <resource-id>
```

### **Kubernetes Issues**

```bash
# Check pod status
kubectl get pods --show-labels
kubectl get pods -o wide

# Check resource usage
kubectl describe node <node-name>
kubectl top nodes
kubectl top pods

# Check events
kubectl get events --field-selector involvedObject.name=<pod-name>
```

## 📝 Useful Aliases

Add these to your `~/.bashrc` or `~/.zshrc`:

```bash
# Infrastructure aliases
alias tf='terragrunt'
alias tfa='terragrunt apply'
alias tfp='terragrunt plan'
alias tfd='terragrunt destroy'
alias tfo='terragrunt output'

# Kubernetes aliases
alias k='kubectl'
alias kgp='kubectl get pods'
alias kgs='kubectl get services'
alias kgn='kubectl get nodes'
alias kdp='kubectl describe pod'
alias kl='kubectl logs'
alias kex='kubectl exec -it'

# Project aliases
alias deploy='./scripts/infra-manager.sh deploy'
alias destroy='./scripts/infra-manager.sh destroy'
alias status='./scripts/infra-manager.sh status'
alias kube-setup='./scripts/kube-context.sh full-setup'
alias kube-update='./scripts/kube-context.sh update'
alias autoscaler-status='./scripts/kube-context.sh autoscaler-status'
alias sync-states='./scripts/state-sync.sh sync-up'
```

## 🎯 Quick Reference

### **Essential Workflow**

```bash
# 1. Initial setup
./scripts/install.sh
oci setup config
cp common-vars.yaml.example common-vars.yaml
# Edit common-vars.yaml with your details

# 2. Deploy infrastructure
./scripts/infra-manager.sh deploy

# 3. Configure Kubernetes + Cluster Autoscaler
./scripts/kube-context.sh full-setup

# 4. Backup states
./scripts/state-sync.sh sync-up

# 5. Check status
./scripts/infra-manager.sh status
kubectl get nodes
```

### **Emergency Commands**

```bash
# Quick cluster info
./scripts/kube-context.sh info

# Emergency destroy (with confirmation)
./scripts/infra-manager.sh destroy-sequential

# Force state sync
./scripts/state-sync.sh sync-up

# Reset kubectl context
./scripts/kube-context.sh update
```

### **Daily Operations**

```bash
# Check infrastructure status
./scripts/infra-manager.sh status

# Check cluster health
kubectl get nodes
kubectl get pods -A

# Monitor resources
kubectl top nodes
kubectl top pods -A

# Backup states
./scripts/state-sync.sh sync-up
```

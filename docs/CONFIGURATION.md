# ⚙️ Configuration Guide

## 📋 Overview

This guide covers all configuration options available in the Oracle Cloud Infrastructure setup. All configuration is centralized in `common-vars.yaml` for consistency and maintainability.

## 🔧 Main Configuration File

### **common-vars.yaml Structure**

```yaml
# =============================================================================
# GLOBAL CONFIGURATION
# =============================================================================
shared:
  ssh_public_key: "ssh-rsa AAAAB3NzaC1yc2E... your-ssh-key"
  vcn_cidr: "10.0.0.0/16"
  internal_cidr: "10.0.0.0/16"

oci:
  region: "us-phoenix-1"
  tenancy_ocid: "ocid1.tenancy.oc1..xxx"
  user_ocid: "ocid1.user.oc1..xxx"
  compartment_ocid: "ocid1.compartment.oc1..xxx"
  private_key_path: "~/.oci/oci_api_key.pem"
  fingerprint: "aa:bb:cc:dd:ee:ff:00:11:22:33:44:55:66:77:88:99"

environment:
  project_name: "oracle"
  name: "dev"

# Module-specific configurations...
```

## 🌐 Network Configuration

### **Basic Network Settings**

```yaml
network:
  vcn_cidr: "10.0.0.0/16"
  
  # Public subnets (internet-facing)
  public_subnets:
    - cidr: "********/24"   # AD-1
    - cidr: "********/24"   # AD-2
    - cidr: "********/24"   # AD-3
  
  # Private subnets (internal)
  private_subnets:
    - cidr: "*********/24"  # AD-1
    - cidr: "*********/24"  # AD-2
    - cidr: "*********/24"  # AD-3
  
  # OKE regional subnet
  oke_regional_subnet:
    cidr: "*********/24"
```

### **Security Rules Configuration**

```yaml
security_rules:
  web:
    - port: 80
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTP traffic"
    - port: 443
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "HTTPS traffic"

  ssh:
    - port: 22
      protocol: "TCP"
      source: "0.0.0.0/0"  # Restrict in production
      description: "SSH access"

  database:
    - port: 5432
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "PostgreSQL access"

  kubernetes:
    - port: 6443
      protocol: "TCP"
      source: "0.0.0.0/0"  # API server access
      description: "Kubernetes API server"
    - port: 12250
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "OKE worker communication"
    - port_range: "30000-32767"
      protocol: "TCP"
      source: "0.0.0.0/0"
      description: "NodePort services"
    - port: 10256
      protocol: "TCP"
      source: "10.0.0.0/16"
      description: "kube-proxy health check"
```

## 🗄️ PostgreSQL Configuration

### **Database Settings**

```yaml
postgresql:
  # Database configuration
  db_name: "snapdb"
  db_username: "snapuser"
  db_password: "SnapStore_321#"  # Use OCI Vault in production
  db_version: "14"

  # Instance configuration
  instance_shape: "PostgreSQL.VM.Standard.E4.Flex.2.32GB"
  storage_size_gb: 256

  # Backup configuration
  backup_retention_days: 7
  enable_backup: true
  backup_window_start_time: "02:00"

  # High availability
  high_availability: false
  monitoring_enabled: true
```

### **Available Instance Shapes**

| Shape | OCPUs | Memory | Use Case |
|-------|-------|--------|----------|
| `PostgreSQL.VM.Standard.E4.Flex.1.16GB` | 1 | 16GB | Development |
| `PostgreSQL.VM.Standard.E4.Flex.2.32GB` | 2 | 32GB | Small Production |
| `PostgreSQL.VM.Standard.E4.Flex.4.64GB` | 4 | 64GB | Medium Production |
| `PostgreSQL.VM.Standard.E4.Flex.8.128GB` | 8 | 128GB | Large Production |

## ☸️ Kubernetes Configuration

### **Cluster Settings**

```yaml
kubernetes:
  kubernetes_version: "v1.33.1"
  api_access_cidr: "0.0.0.0/0"  # Restrict in production
  enable_monitoring: true
  
  # Pod and service networking
  pods_cidr: "10.244.0.0/16"
  services_cidr: "10.96.0.0/16"

  # Node pools configuration
  node_pools:
    workers:
      node_shape: "VM.Standard.E3.Flex"
      node_shape_memory_gb: 8
      node_shape_ocpus: 2
      size: 1
      max_size: 10
      min_size: 1
      boot_volume_size_gb: 50
      
      # Node labels
      labels:
        environment: "dev"
        managed-by: "terragrunt"
        pool-type: "workers"
```

### **Available Node Shapes**

| Shape | OCPUs | Memory | Use Case |
|-------|-------|--------|----------|
| `VM.Standard.E3.Flex` | 1-64 | 1-1024GB | General purpose |
| `VM.Standard.E4.Flex` | 1-64 | 1-1024GB | Latest generation |
| `VM.Standard.A1.Flex` | 1-80 | 1-512GB | ARM-based (cost-effective) |
| `VM.DenseIO.E4.Flex` | 1-64 | 1-1024GB | High I/O workloads |

### **Kubernetes Versions**

Available versions (check OCI console for latest):
- `v1.33.1` (Latest)
- `v1.32.x`
- `v1.31.x`

## 📦 Object Storage Configuration

### **State Management Settings**

```yaml
object_storage:
  bucket_name: "oracle-dev-terraform-state"
  versioning_enabled: true
  public_access_type: "NoPublicAccess"
  storage_tier: "Standard"
  
  # Lifecycle management
  lifecycle_rules:
    - name: "delete_old_versions"
      is_enabled: true
      target: "previous-object-versions"
      time_amount: 30
      time_unit: "DAYS"
```

## 🏷️ Tagging Strategy

### **Standard Tags**

All resources are automatically tagged with:

```yaml
tags:
  common:
    Environment: "dev"
    Project: "oracle-dev"
    ManagedBy: "Terragrunt"
    Owner: "DevOps Team"
  
  # Module-specific tags
  network:
    Type: "network-infrastructure"
  
  postgresql:
    Type: "database"
    Service: "postgresql"
  
  kubernetes:
    Type: "kubernetes-cluster"
    Service: "oke"
```

## 🔐 Security Configuration

### **SSH Key Management**

```yaml
shared:
  # Generate SSH key pair
  ssh_public_key: "ssh-rsa AAAAB3NzaC1yc2E... your-ssh-key"
```

**Generate SSH key:**
```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
cat ~/.ssh/id_rsa.pub  # Copy this to ssh_public_key
```

### **OCI Authentication**

```yaml
oci:
  region: "us-phoenix-1"
  tenancy_ocid: "ocid1.tenancy.oc1..xxx"
  user_ocid: "ocid1.user.oc1..xxx"
  compartment_ocid: "ocid1.compartment.oc1..xxx"
  private_key_path: "~/.oci/oci_api_key.pem"
  fingerprint: "aa:bb:cc:dd:ee:ff:00:11:22:33:44:55:66:77:88:99"
```

**Get OCIDs:**
```bash
# Tenancy OCID
oci iam tenancy get --tenancy-id $(oci setup config --help | grep tenancy | cut -d: -f2)

# User OCID
oci iam user list --query 'data[0].id' --raw-output

# Compartment OCID
oci iam compartment list --query 'data[0].id' --raw-output
```

## 🌍 Regional Configuration

### **Available Regions**

| Region Code | Region Name | Location |
|-------------|-------------|----------|
| `us-phoenix-1` | US West (Phoenix) | Arizona, USA |
| `us-ashburn-1` | US East (Ashburn) | Virginia, USA |
| `eu-frankfurt-1` | Germany Central (Frankfurt) | Germany |
| `ap-tokyo-1` | Japan East (Tokyo) | Japan |
| `uk-london-1` | UK South (London) | United Kingdom |

### **Region-Specific Settings**

```yaml
oci:
  region: "us-phoenix-1"  # Change to your preferred region
  
# Some resources may have region-specific availability
kubernetes:
  kubernetes_version: "v1.33.1"  # Check available versions per region
```

## 🔧 Environment-Specific Configuration

### **Development Environment**

```yaml
environment:
  project_name: "oracle"
  name: "dev"

postgresql:
  instance_shape: "PostgreSQL.VM.Standard.E4.Flex.1.16GB"
  storage_size_gb: 100
  high_availability: false

kubernetes:
  node_pools:
    workers:
      size: 1
      max_size: 3
      min_size: 1
```

### **Production Environment**

```yaml
environment:
  project_name: "oracle"
  name: "prod"

postgresql:
  instance_shape: "PostgreSQL.VM.Standard.E4.Flex.4.64GB"
  storage_size_gb: 500
  high_availability: true
  backup_retention_days: 30

kubernetes:
  api_access_cidr: "10.0.0.0/16"  # Restrict API access
  node_pools:
    workers:
      size: 3
      max_size: 20
      min_size: 3

security_rules:
  ssh:
    - port: 22
      protocol: "TCP"
      source: "10.0.0.0/16"  # Restrict SSH to VCN only
      description: "SSH access from VCN"
```

## 🔄 Configuration Validation

### **Validate Configuration**

```bash
# Validate all configurations
terragrunt run-all validate

# Validate specific module
cd infrastructure/network
terragrunt validate
```

### **Common Configuration Issues**

1. **Invalid OCIDs**: Ensure all OCIDs are correct and accessible
2. **SSH Key Format**: Must be in OpenSSH format
3. **CIDR Conflicts**: Ensure subnet CIDRs don't overlap
4. **Region Availability**: Check if resources are available in your region

## 📝 Configuration Best Practices

### **Security**
- Use OCI Vault for sensitive data in production
- Restrict SSH access to known IP ranges
- Enable monitoring and alerting
- Use least privilege access principles

### **Scalability**
- Plan subnet sizes for future growth
- Use flexible instance shapes
- Configure auto-scaling appropriately
- Monitor resource utilization

### **Cost Optimization**
- Right-size instances for workload
- Use auto-scaling to optimize costs
- Consider ARM-based instances for cost savings
- Implement lifecycle policies for storage

### **Maintainability**
- Use consistent naming conventions
- Document custom configurations
- Version control all changes
- Test configurations in development first

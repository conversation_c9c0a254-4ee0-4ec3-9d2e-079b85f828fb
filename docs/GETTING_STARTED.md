# 🚀 Getting Started with Oracle Cloud Infrastructure

## 📋 Prerequisites

- Oracle Cloud account with admin access
- OCI CLI installed and configured
- Terraform >= 1.0
- Terragrunt >= 0.45
- kubectl (for Kubernetes management)

## 🛠️ Step 1: Initial Setup

### 1.1 Install Dependencies
```bash
# Run the installation script
./scripts/install.sh
```

### 1.2 Configure OCI CLI
```bash
# Configure OCI CLI (if not already done)
oci setup config

# Test configuration
oci iam user get --user-id $(oci iam user list --query 'data[0].id' --raw-output)
```

### 1.3 Configure Project Variables
```bash
# Copy and edit configuration
cp common-vars.yaml.example common-vars.yaml

# Edit with your OCI details
nano common-vars.yaml
```

**Required changes in `common-vars.yaml`:**
- `compartment_ocid`: Your OCI compartment OCID
- `region`: Your preferred OCI region
- `ssh_public_key`: Your SSH public key

## 🏗️ Step 2: Deploy Infrastructure

### 2.1 Deploy All Resources (Recommended)
```bash
# Deploy everything at once
./scripts/infra-manager.sh deploy

# Or with auto-approval (no prompts)
./scripts/infra-manager.sh deploy --auto-approve
```

### 2.2 Deploy Step-by-Step (Safer)
```bash
# Deploy one module at a time
./scripts/infra-manager.sh deploy-sequential
```

### 2.3 Manual Deployment (Advanced)
```bash
# 1. Object Storage (for state management)
cd infrastructure/object-storage
terragrunt apply
cd ../..

# 2. Network (foundation)
cd infrastructure/network
terragrunt apply
cd ../..

# 3. PostgreSQL (database)
cd infrastructure/postgresql
terragrunt apply
cd ../..

# 4. Kubernetes (container platform)
cd infrastructure/kubernetes
terragrunt apply
cd ../..
```

## ☸️ Step 3: Configure Kubernetes Access

### 3.1 Set Up kubectl
```bash
# Configure kubectl for the cluster
./scripts/kube-context.sh update

# Test connectivity
./scripts/kube-context.sh test
```

### 3.2 Verify Cluster
```bash
# Check cluster status
kubectl get nodes

# Check system pods
kubectl get pods -A
```

## 📦 Step 4: State Management

### 4.1 Backup States
```bash
# Upload all states to OCI Object Storage
./scripts/state-sync.sh sync-up

# List backed up states
./scripts/state-sync.sh list
```

### 4.2 Download States (Team Collaboration)
```bash
# Download all states from OCI Object Storage
./scripts/state-sync.sh sync-down
```

## 📊 Step 5: Verify Deployment

### 5.1 Check Infrastructure Status
```bash
# Show deployment summary
./scripts/infra-manager.sh status

# Check Kubernetes cluster
./scripts/kube-context.sh info
```

### 5.2 Test Connectivity
```bash
# Test Kubernetes API
curl -k https://$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}' | cut -d'/' -f3)/version

# Test database connectivity (if needed)
# Connect using the PostgreSQL outputs
```

## 🔥 Step 6: Clean Up (When Done)

### 6.1 Destroy All Resources
```bash
# Destroy everything (CAUTION: This deletes all resources!)
./scripts/infra-manager.sh destroy

# Or destroy step-by-step (safer)
./scripts/infra-manager.sh destroy-sequential
```

### 6.2 Manual Cleanup
```bash
# Destroy in reverse order
cd infrastructure/kubernetes && terragrunt destroy && cd ../..
cd infrastructure/postgresql && terragrunt destroy && cd ../..
cd infrastructure/network && terragrunt destroy && cd ../..
cd infrastructure/object-storage && terragrunt destroy && cd ../..
```

## 🎯 Quick Reference

### Essential Commands
```bash
# Deploy everything
./scripts/infra-manager.sh deploy

# Check status
./scripts/infra-manager.sh status

# Configure kubectl
./scripts/kube-context.sh update

# Backup states
./scripts/state-sync.sh sync-up

# Destroy everything
./scripts/infra-manager.sh destroy
```

### Troubleshooting
```bash
# Plan changes without applying
./scripts/infra-manager.sh plan

# Test Kubernetes connectivity
./scripts/kube-context.sh test

# Check state files
./scripts/state-sync.sh list
```

## 📚 Next Steps

- **Deploy Applications**: Use kubectl to deploy your workloads
- **Configure Monitoring**: Set up monitoring and logging
- **Scale Resources**: Adjust node pools and database sizes
- **Team Setup**: Share kubeconfig and state management

## 🆘 Need Help?

- Check [Troubleshooting Guide](TROUBLESHOOTING.md)
- Review [State Management](STATE_MANAGEMENT.md)
- See [Architecture Overview](ARCHITECTURE.md)

# ☸️ Kubernetes Guide

## 🎯 Overview

This guide covers managing the Oracle Kubernetes Engine (OKE) cluster deployed by this infrastructure. The cluster is production-ready with auto-scaling, monitoring, and proper security configuration.

## 🚀 Quick Start

### **Configure kubectl Access**

```bash
# Configure kubectl for the cluster
./scripts/kube-context.sh update

# Test connectivity
./scripts/kube-context.sh test

# Show cluster information
./scripts/kube-context.sh info
```

### **Verify Cluster Status**

```bash
# Check cluster info
kubectl cluster-info

# List nodes
kubectl get nodes -o wide

# Check system pods
kubectl get pods -A
```

## 🔧 Cluster Configuration

### **Cluster Details**
- **Name**: `oracle-dev-oke`
- **Version**: `v1.33.1`
- **API Endpoint**: `https://*************:6443`
- **Network**: Regional subnet with public access
- **CNI**: Flannel overlay networking

### **Node Pool Configuration**
- **Pool Name**: `oracle-dev-workers`
- **Node Shape**: `VM.Standard.E3.Flex` (2 OCPUs, 8GB RAM)
- **Auto-scaling**: 1-10 nodes
- **Boot Volume**: 50GB per node
- **Placement**: Across all 3 availability domains

## 📦 Deploying Applications

### **Basic Deployment Example**

```yaml
# nginx-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.21
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer
```

```bash
# Deploy the application
kubectl apply -f nginx-deployment.yaml

# Check deployment status
kubectl get deployments
kubectl get pods
kubectl get services
```

### **Load Balancer Services**

The cluster is configured with NodePort access (30000-32767) for load balancer integration:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: my-app-loadbalancer
spec:
  type: LoadBalancer
  selector:
    app: my-app
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
```

## 🔍 Monitoring & Observability

### **Cluster Monitoring**

```bash
# Check cluster resource usage
kubectl top nodes
kubectl top pods -A

# View cluster events
kubectl get events --sort-by=.metadata.creationTimestamp

# Check node conditions
kubectl describe nodes
```

### **System Components Status**

```bash
# Check system pods
kubectl get pods -n kube-system

# CoreDNS status
kubectl get pods -n kube-system -l k8s-app=kube-dns

# Check kube-proxy
kubectl get pods -n kube-system -l k8s-app=kube-proxy

# Flannel networking
kubectl get pods -n kube-system -l app=flannel
```

## 📈 Scaling Operations

### **Manual Scaling**

```bash
# Scale deployment
kubectl scale deployment nginx-deployment --replicas=5

# Scale node pool (via OCI Console or CLI)
oci ce node-pool update --node-pool-id <node-pool-id> --size 3
```

### **Horizontal Pod Autoscaler**

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nginx-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nginx-deployment
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

### **Cluster Autoscaler**

The cluster autoscaler is configured to automatically scale nodes based on pod resource requests:

- **Min Nodes**: 1
- **Max Nodes**: 10
- **Scale-up**: When pods can't be scheduled
- **Scale-down**: When nodes are underutilized

## 🔐 Security & Access Control

### **RBAC Configuration**

```yaml
# Create a service account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: my-service-account
  namespace: default
---
# Create a role
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: default
  name: pod-reader
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "watch", "list"]
---
# Bind role to service account
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: read-pods
  namespace: default
subjects:
- kind: ServiceAccount
  name: my-service-account
  namespace: default
roleRef:
  kind: Role
  name: pod-reader
  apiGroup: rbac.authorization.k8s.io
```

### **Network Policies**

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
```

## 💾 Storage Management

### **Persistent Volumes**

```yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: my-pvc
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: oci-bv
  resources:
    requests:
      storage: 50Gi
```

### **Available Storage Classes**

```bash
# List available storage classes
kubectl get storageclass

# OCI Block Volume (default)
# - oci-bv: Standard block volumes
# - oci-bv-encrypted: Encrypted block volumes
```

## 🔧 Troubleshooting

### **Common Issues**

#### **Pods Stuck in Pending**
```bash
# Check pod events
kubectl describe pod <pod-name>

# Check node resources
kubectl describe nodes

# Check if PVC is bound
kubectl get pvc
```

#### **Service Not Accessible**
```bash
# Check service endpoints
kubectl get endpoints <service-name>

# Check if pods are ready
kubectl get pods -l app=<app-label>

# Test service connectivity
kubectl run test-pod --image=busybox --rm -it -- wget -qO- <service-name>
```

#### **Node Issues**
```bash
# Check node status
kubectl get nodes
kubectl describe node <node-name>

# Check node logs (via OCI Console)
# SSH to node and check kubelet logs
sudo journalctl -u kubelet
```

### **Debugging Commands**

```bash
# Get cluster information
kubectl cluster-info dump

# Check resource quotas
kubectl get resourcequota -A

# Check limit ranges
kubectl get limitrange -A

# View all resources in namespace
kubectl get all -n <namespace>
```

## 🔄 Backup & Recovery

### **Backup Strategies**

1. **Application Data**: Use persistent volumes with snapshots
2. **Configuration**: Store manifests in version control
3. **Secrets**: Backup using sealed-secrets or external secret management

```bash
# Backup all resources in namespace
kubectl get all -o yaml -n <namespace> > backup.yaml

# Backup specific resource types
kubectl get configmaps,secrets -o yaml -n <namespace> > configs-backup.yaml
```

### **Disaster Recovery**

```bash
# Restore from backup
kubectl apply -f backup.yaml

# Recreate namespace
kubectl create namespace <namespace>
kubectl apply -f configs-backup.yaml -n <namespace>
```

## 📊 Performance Optimization

### **Resource Management**

```yaml
# Set resource requests and limits
resources:
  requests:
    memory: "64Mi"
    cpu: "250m"
  limits:
    memory: "128Mi"
    cpu: "500m"
```

### **Node Affinity**

```yaml
# Prefer specific nodes
affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      preference:
        matchExpressions:
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
          - VM.Standard.E3.Flex
```

## 🛠️ Useful Commands

### **Cluster Management**

```bash
# Context management
./scripts/kube-context.sh info
./scripts/kube-context.sh test
./scripts/kube-context.sh update

# Resource inspection
kubectl get all -A
kubectl describe node <node-name>
kubectl logs -f <pod-name>

# Debugging
kubectl exec -it <pod-name> -- /bin/bash
kubectl port-forward <pod-name> 8080:80
kubectl proxy
```

### **Application Management**

```bash
# Deployment operations
kubectl apply -f manifest.yaml
kubectl delete -f manifest.yaml
kubectl rollout status deployment/<deployment-name>
kubectl rollout undo deployment/<deployment-name>

# Scaling
kubectl scale deployment <deployment-name> --replicas=3
kubectl autoscale deployment <deployment-name> --cpu-percent=70 --min=2 --max=10
```

## 🎯 Best Practices

### **Resource Management**
- Always set resource requests and limits
- Use namespaces for organization
- Implement resource quotas
- Monitor resource usage

### **Security**
- Use RBAC for access control
- Implement network policies
- Scan images for vulnerabilities
- Use secrets for sensitive data

### **Reliability**
- Use health checks (liveness/readiness probes)
- Implement proper logging
- Use multiple replicas for high availability
- Plan for disaster recovery

### **Performance**
- Use appropriate node sizes
- Implement horizontal pod autoscaling
- Optimize container images
- Monitor and tune resource allocation

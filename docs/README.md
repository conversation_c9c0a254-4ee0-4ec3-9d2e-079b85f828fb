# 📖 Oracle Cloud Infrastructure Documentation

## 🚀 Quick Start

**New to this project?** Start here: [Getting Started Guide](GETTING_STARTED.md)

## 📚 Documentation Index

### 🏗️ **Setup & Deployment**
- **[Getting Started](GETTING_STARTED.md)** - Complete step-by-step setup guide
- **[Architecture Overview](ARCHITECTURE.md)** - Infrastructure design and components
- **[Configuration Guide](CONFIGURATION.md)** - Detailed configuration options

### 🔧 **Management & Operations**
- **[State Management](STATE_MANAGEMENT.md)** - Terraform state backup and sync
- **[Kubernetes Guide](KUBERNETES.md)** - Cluster management and kubectl setup
- **[Troubleshooting](TROUBLESHOOTING.md)** - Common issues and solutions

### 📋 **Reference**
- **[Commands Reference](COMMANDS.md)** - All available scripts and commands
- **[Security Guide](SECURITY.md)** - Security best practices and configuration

## 🎯 What This Project Provides

### **Infrastructure Components**
- ✅ **Object Storage** - Terraform state management with versioning
- ✅ **Network** - VCN with public/private subnets and security groups
- ✅ **PostgreSQL** - Managed database with high availability
- ✅ **Kubernetes** - OKE cluster with auto-scaling worker nodes
- ✅ **Security** - Proper network isolation and access controls

### **Management Tools**
- 🛠️ **Infrastructure Manager** - Deploy/destroy all resources
- ☸️ **Kubernetes Context** - Easy kubectl configuration
- 📦 **State Sync** - Backup states to OCI Object Storage
- 🔧 **Installation Script** - Automated dependency setup

## 🚀 Quick Commands

```bash
# 🏗️ Deploy everything
./scripts/infra-manager.sh deploy

# ☸️ Configure kubectl
./scripts/kube-context.sh update

# 📦 Backup states
./scripts/state-sync.sh sync-up

# 📊 Check status
./scripts/infra-manager.sh status

# 🔥 Destroy everything
./scripts/infra-manager.sh destroy
```

## 🏗️ Project Structure

```
oracle-cloud-infrastructure/
├── 📁 infrastructure/          # Terragrunt modules
│   ├── network/               # VCN, subnets, security
│   ├── postgresql/            # Database infrastructure
│   ├── kubernetes/            # OKE cluster
│   ├── cluster-autoscaler/    # Kubernetes autoscaling
│   └── object-storage/        # State management
├── 📁 modules/                # Terraform modules
├── 📁 scripts/                # Management scripts
├── 📁 docs/                   # Documentation
├── 📄 common-vars.yaml        # Configuration
└── 📄 root.hcl               # Terragrunt root config
```

## 🎯 Deployment Flow

1. **Configure** - Edit `common-vars.yaml` with your OCI details
2. **Deploy** - Run `./scripts/infra-manager.sh deploy`
3. **Access** - Configure kubectl with `./scripts/kube-context.sh update`
4. **Backup** - Sync states with `./scripts/state-sync.sh sync-up`
5. **Use** - Deploy your applications to the cluster

## 🔐 Security Features

- **Network Isolation** - Separate public/private subnets
- **Security Groups** - Minimal required ports only
- **State Encryption** - Encrypted state storage in OCI
- **Access Control** - OCI IAM integration
- **SSH Keys** - Secure instance access

## 🌟 Key Benefits

- **Production Ready** - Optimized for real workloads
- **Team Friendly** - Shared state and collaboration tools
- **Cost Optimized** - Right-sized resources with auto-scaling
- **Maintainable** - Clean module structure and documentation
- **Recoverable** - Automated backups and disaster recovery

## 📞 Support

- **Issues**: Check [Troubleshooting Guide](TROUBLESHOOTING.md)
- **Configuration**: See [Configuration Guide](CONFIGURATION.md)
- **Kubernetes**: Review [Kubernetes Guide](KUBERNETES.md)

---

**Ready to get started?** → [Getting Started Guide](GETTING_STARTED.md)







# 🔐 Security Guide

## 🎯 Security Overview

This guide covers security best practices and configurations for the Oracle Cloud Infrastructure deployment. The infrastructure is designed with security-first principles and defense-in-depth strategies.

## 🌐 Network Security

### **VCN Security Architecture**

```
Security Layers:
├── 🌍 Internet Gateway (Public Subnets Only)
├── 🔄 NAT Gateway (Private Subnet Outbound)
├── 🛡️ Security Lists (Subnet-level Firewall)
├── 🔒 Network Security Groups (Instance-level Firewall)
└── 🚪 Route Tables (Traffic Direction)
```

### **Security Lists Configuration**

#### **Public Subnet Security List**
```yaml
Ingress Rules:
- Port 80/443: HTTP/HTTPS from 0.0.0.0/0
- Port 6443: Kubernetes API from 0.0.0.0/0
- Port 30000-32767: NodePort services from 0.0.0.0/0
- Port 22: SSH from restricted CIDR (production)

Egress Rules:
- All traffic to 0.0.0.0/0 (outbound internet)
```

#### **Private Subnet Security List**
```yaml
Ingress Rules:
- Port 5432: PostgreSQL from VCN (10.0.0.0/16)
- Port 12250: OKE worker communication from VCN
- Port 10256: kube-proxy health check from VCN
- All traffic from VCN (10.0.0.0/16)

Egress Rules:
- All traffic to 0.0.0.0/0 (via NAT Gateway)
```

### **Network Security Groups**

```yaml
# Kubernetes API Security Group
oke_api_nsg:
  ingress:
    - port: 6443
      protocol: TCP
      source: 0.0.0.0/0  # Restrict in production
      description: "Kubernetes API access"

# Worker Node Security Group
oke_worker_nsg:
  ingress:
    - protocol: ALL
      source: self  # Allow communication between workers
      description: "Inter-node communication"
```

## 🔑 Identity & Access Management

### **OCI IAM Configuration**

#### **Required Policies**
```
# Compute and Network Management
allow group DevOpsTeam to manage virtual-network-family in compartment oracle-dev
allow group DevOpsTeam to manage instance-family in compartment oracle-dev

# Kubernetes Management
allow group DevOpsTeam to manage cluster-family in compartment oracle-dev
allow group DevOpsTeam to manage container-family in compartment oracle-dev

# Database Management
allow group DevOpsTeam to manage postgresql-family in compartment oracle-dev

# Object Storage (for Terraform state)
allow group DevOpsTeam to manage buckets in compartment oracle-dev
allow group DevOpsTeam to manage objects in compartment oracle-dev
```

#### **Service Account Configuration**
```bash
# Create dedicated service account for automation
oci iam user create --name terraform-service-account --description "Terraform automation"

# Generate API key
oci iam user api-key upload --user-id <user-ocid> --key-file ~/.oci/terraform_api_key_public.pem

# Add to appropriate group
oci iam group-membership create --group-id <group-ocid> --user-id <user-ocid>
```

### **SSH Key Management**

#### **Generate Secure SSH Keys**
```bash
# Generate 4096-bit RSA key
ssh-keygen -t rsa -b 4096 -C "oracle-infrastructure-$(date +%Y%m%d)" -f ~/.ssh/oracle_infrastructure

# Set proper permissions
chmod 600 ~/.ssh/oracle_infrastructure
chmod 644 ~/.ssh/oracle_infrastructure.pub

# Add to common-vars.yaml
cat ~/.ssh/oracle_infrastructure.pub
```

#### **SSH Access Restrictions**
```yaml
# Production configuration
security_rules:
  ssh:
    - port: 22
      protocol: "TCP"
      source: "10.0.0.0/16"  # VCN only
      description: "SSH access from VCN"
    - port: 22
      protocol: "TCP"
      source: "***********/24"  # Your office IP range
      description: "SSH access from office"
```

## ☸️ Kubernetes Security

### **RBAC Configuration**

#### **Cluster Admin Role**
```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: admin-user
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: User
  name: <EMAIL>
  apiGroup: rbac.authorization.k8s.io
```

#### **Developer Role**
```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: development
  name: developer
rules:
- apiGroups: ["", "apps", "extensions"]
  resources: ["pods", "deployments", "services", "configmaps"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: developer-binding
  namespace: development
subjects:
- kind: User
  name: <EMAIL>
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: developer
  apiGroup: rbac.authorization.k8s.io
```

### **Pod Security Standards**

#### **Pod Security Policy**
```yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: restricted
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

#### **Network Policies**
```yaml
# Deny all traffic by default
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
# Allow specific traffic
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-web-traffic
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: web
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: frontend
    ports:
    - protocol: TCP
      port: 8080
```

## 🗄️ Database Security

### **PostgreSQL Security Configuration**

#### **Network Access Control**
```yaml
postgresql:
  # Network security
  subnet_type: "private"  # Deploy in private subnet only
  allowed_cidrs:
    - "10.0.0.0/16"  # VCN access only
  
  # Access control
  db_username: "app_user"  # Don't use default postgres user
  db_password: "{{ vault_password }}"  # Use OCI Vault in production
  
  # Encryption
  encryption_at_rest: true
  encryption_in_transit: true
```

#### **Database User Management**
```sql
-- Create application-specific users
CREATE USER app_readonly WITH PASSWORD 'secure_password';
CREATE USER app_readwrite WITH PASSWORD 'secure_password';

-- Grant minimal required permissions
GRANT CONNECT ON DATABASE snapdb TO app_readonly;
GRANT USAGE ON SCHEMA public TO app_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO app_readonly;

GRANT CONNECT ON DATABASE snapdb TO app_readwrite;
GRANT USAGE ON SCHEMA public TO app_readwrite;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_readwrite;
```

### **Backup Security**
```yaml
postgresql:
  backup_encryption: true
  backup_retention_days: 30
  backup_window_start_time: "02:00"  # Low-traffic window
  cross_region_backup: true  # For disaster recovery
```

## 🔒 Data Protection

### **Encryption Configuration**

#### **Encryption at Rest**
```yaml
# All storage encrypted by default
storage:
  block_volumes:
    encryption: "OCI_MANAGED"  # or "CUSTOMER_MANAGED"
  
  object_storage:
    encryption: "AES256"
    
  database:
    encryption: "TRANSPARENT_DATA_ENCRYPTION"
```

#### **Encryption in Transit**
```yaml
# TLS configuration
tls:
  kubernetes_api: "TLS 1.2+"
  database_connections: "SSL/TLS required"
  object_storage: "HTTPS only"
  oci_load_balancers: "SSL termination (when using LoadBalancer services)"
```

### **Secrets Management**

#### **Kubernetes Secrets**
```yaml
# Use external secret management
apiVersion: v1
kind: Secret
metadata:
  name: database-credentials
type: Opaque
data:
  username: <base64-encoded>
  password: <base64-encoded>
```

#### **OCI Vault Integration**
```bash
# Store secrets in OCI Vault
oci vault secret create-base64 \
  --compartment-id <compartment-ocid> \
  --vault-id <vault-ocid> \
  --key-id <key-ocid> \
  --secret-name "database-password" \
  --secret-content-content "secure_password"
```

## 🔍 Monitoring & Auditing

### **Security Monitoring**

#### **OCI Audit Logs**
```yaml
audit_configuration:
  enabled: true
  retention_period: "365 days"
  
  monitored_events:
    - "iam_authentication"
    - "compute_instance_access"
    - "database_connections"
    - "object_storage_access"
    - "network_security_changes"
```

#### **Kubernetes Audit Logging**
```yaml
# Enable audit logging in OKE
apiVersion: audit.k8s.io/v1
kind: Policy
rules:
- level: Metadata
  namespaces: ["production"]
  resources:
  - group: ""
    resources: ["secrets", "configmaps"]
- level: RequestResponse
  resources:
  - group: "rbac.authorization.k8s.io"
    resources: ["roles", "rolebindings", "clusterroles", "clusterrolebindings"]
```

### **Security Alerts**

#### **OCI Monitoring Alarms**
```bash
# Create security-related alarms
oci monitoring alarm create \
  --display-name "Failed SSH Attempts" \
  --compartment-id <compartment-ocid> \
  --metric-compartment-id <compartment-ocid> \
  --namespace "oci_computeagent" \
  --query "failed_ssh_attempts[1m].count() > 5"
```

## 🛡️ Security Best Practices

### **Infrastructure Security**

1. **Principle of Least Privilege**
   - Grant minimal required permissions
   - Use separate service accounts for different functions
   - Regularly review and audit permissions

2. **Network Segmentation**
   - Use private subnets for sensitive resources
   - Implement network security groups
   - Restrict inter-subnet communication

3. **Regular Updates**
   - Keep Kubernetes version updated
   - Apply security patches promptly
   - Update base images regularly

### **Application Security**

1. **Container Security**
   ```yaml
   # Security context for pods
   securityContext:
     runAsNonRoot: true
     runAsUser: 1000
     readOnlyRootFilesystem: true
     allowPrivilegeEscalation: false
     capabilities:
       drop:
         - ALL
   ```

2. **Image Security**
   ```bash
   # Scan images for vulnerabilities
   docker scan myapp:latest
   
   # Use minimal base images
   FROM alpine:3.18
   # or
   FROM gcr.io/distroless/java:11
   ```

### **Operational Security**

1. **Access Control**
   - Use multi-factor authentication
   - Implement just-in-time access
   - Regular access reviews

2. **Backup & Recovery**
   - Encrypted backups
   - Regular restore testing
   - Cross-region backup storage

3. **Incident Response**
   - Security incident playbooks
   - Automated threat detection
   - Regular security drills

## 🚨 Security Checklist

### **Pre-Production Checklist**

- [ ] Network security groups configured
- [ ] SSH access restricted to known IPs
- [ ] Database in private subnet
- [ ] Encryption enabled for all storage
- [ ] RBAC configured for Kubernetes
- [ ] Secrets stored securely (OCI Vault)
- [ ] Audit logging enabled
- [ ] Security monitoring configured
- [ ] Backup encryption enabled
- [ ] Network policies implemented

### **Regular Security Reviews**

- [ ] Review IAM permissions (monthly)
- [ ] Update Kubernetes version (quarterly)
- [ ] Security patch management (ongoing)
- [ ] Audit log review (weekly)
- [ ] Penetration testing (annually)
- [ ] Disaster recovery testing (quarterly)

## 📞 Security Incident Response

### **Immediate Actions**
1. **Isolate affected resources**
2. **Preserve evidence**
3. **Notify security team**
4. **Document timeline**

### **Investigation Commands**
```bash
# Check recent access
oci audit event list --start-time 2024-01-01T00:00:00Z

# Review Kubernetes events
kubectl get events --sort-by=.metadata.creationTimestamp

# Check failed authentication
grep "authentication failure" /var/log/auth.log
```

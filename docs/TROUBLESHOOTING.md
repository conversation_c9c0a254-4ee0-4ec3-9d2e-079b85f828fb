# 🔧 Troubleshooting Guide

## 🚨 Common Issues & Solutions

### **🔐 Authentication Issues**

#### **OCI CLI Authentication Failed**
```
Error: Service error:NotAuthenticated. The required information to complete authentication was not provided.
```

**Solution:**
```bash
# Reconfigure OCI CLI
oci setup config

# Verify configuration
oci iam user get --user-id $(oci iam user list --query 'data[0].id' --raw-output)

# Check config file
cat ~/.oci/config

# Verify private key permissions
chmod 600 ~/.oci/oci_api_key.pem
```

#### **Invalid OCID Format**
```
Error: Invalid OCID format
```

**Solution:**
```bash
# Get correct OCIDs
oci iam tenancy get --tenancy-id <tenancy-ocid>
oci iam user list --query 'data[0].id' --raw-output
oci iam compartment list --query 'data[0].id' --raw-output

# Update common-vars.yaml with correct OCIDs
```

### **🏗️ Infrastructure Deployment Issues**

#### **Terragrunt Apply Fails**
```
Error: Error creating/updating resource
```

**Solution:**
```bash
# Check detailed error
cd infrastructure/<module>
terragrunt plan

# Clear cache and retry
rm -rf .terragrunt-cache/
terragrunt apply

# Check resource limits in OCI console
# Verify compartment permissions
```

#### **Resource Already Exists**
```
Error: Resource already exists
```

**Solution:**
```bash
# Import existing resource
terragrunt import <resource-type>.<resource-name> <resource-id>

# Or destroy and recreate
terragrunt destroy
terragrunt apply
```

#### **Network Connectivity Issues**
```
Error: timeout while waiting for state to become 'AVAILABLE'
```

**Solution:**
```bash
# Check security list rules
# Verify subnet configuration
# Check route table settings
# Ensure internet gateway is attached

# For Kubernetes specifically, verify these ports:
# - 6443 (API server)
# - 12250 (Worker communication)
# - 30000-32767 (NodePort range)
# - 10256 (kube-proxy health)
```

### **☸️ Kubernetes Issues**

#### **kubectl Connection Timeout**
```
Error: Unable to connect to the server: dial tcp: i/o timeout
```

**Solution:**
```bash
# Update kubeconfig
./scripts/kube-context.sh update

# Test connectivity
curl -k https://<cluster-endpoint>:6443/version

# Check network security groups
# Verify API server endpoint accessibility
# Check VPN/firewall settings
```

#### **Nodes Not Ready**
```
NAME          STATUS     ROLES   AGE
***********   NotReady   node    5m
```

**Solution:**
```bash
# Check node details
kubectl describe node <node-name>

# Check system pods
kubectl get pods -n kube-system

# Common fixes:
# 1. Wait for node initialization (can take 5-10 minutes)
# 2. Check if CNI pods are running
kubectl get pods -n kube-system -l app=flannel

# 3. Restart kubelet (SSH to node)
sudo systemctl restart kubelet
```

#### **Pods Stuck in Pending**
```
NAME                     READY   STATUS    RESTARTS   AGE
my-app-xxx               0/1     Pending   0          5m
```

**Solution:**
```bash
# Check pod events
kubectl describe pod <pod-name>

# Common causes and fixes:
# 1. Insufficient resources
kubectl top nodes
kubectl describe nodes

# 2. Node selector issues
kubectl get nodes --show-labels

# 3. PVC not bound
kubectl get pvc

# 4. Image pull issues
kubectl describe pod <pod-name> | grep -A 10 Events
```

#### **Service Not Accessible**
```
Error: Connection refused or timeout
```

**Solution:**
```bash
# Check service endpoints
kubectl get endpoints <service-name>

# Check if pods are ready
kubectl get pods -l app=<app-label>

# Test service from within cluster
kubectl run test-pod --image=busybox --rm -it -- wget -qO- <service-name>

# For LoadBalancer services, check OCI Load Balancer console
# Verify NodePort range (30000-32767) is open in security lists
```

### **📦 State Management Issues**

#### **State Sync Fails**
```
Error: Failed to upload state file
```

**Solution:**
```bash
# Check OCI Object Storage permissions
oci os bucket get --bucket-name oracle-dev-terraform-state

# Verify bucket exists
oci os bucket list --query 'data[].name'

# Create bucket if missing
oci os bucket create --name oracle-dev-terraform-state --compartment-id <compartment-ocid>

# Check state file exists
ls -la infrastructure/<module>/.terragrunt-cache/*/terraform.tfstate
```

#### **State Lock Issues**
```
Error: Error acquiring the state lock
```

**Solution:**
```bash
# Force unlock (use with caution)
cd infrastructure/<module>
terragrunt force-unlock <lock-id>

# Or wait for lock to expire (usually 20 minutes)
# Check if another process is running
```

### **🔧 Performance Issues**

#### **Slow Deployment**
```
Resources taking too long to create
```

**Solution:**
```bash
# Check OCI service limits
oci limits resource-availability get --service-name compute

# Monitor deployment progress
terragrunt apply -parallelism=1

# Check OCI console for resource creation status
# Some resources (like databases) can take 10-20 minutes
```

#### **High Resource Usage**
```
Nodes running out of CPU/Memory
```

**Solution:**
```bash
# Check resource usage
kubectl top nodes
kubectl top pods -A

# Scale up node pool
oci ce node-pool update --node-pool-id <node-pool-id> --size 3

# Add resource limits to pods
# Configure horizontal pod autoscaler
# Consider larger node shapes
```

### **🔍 Debugging Commands**

#### **Infrastructure Debugging**
```bash
# Check Terragrunt state
cd infrastructure/<module>
terragrunt show
terragrunt state list

# Check for configuration drift
terragrunt plan -detailed-exitcode

# View detailed logs
terragrunt apply -auto-approve -var-file=terraform.tfvars 2>&1 | tee apply.log
```

#### **Kubernetes Debugging**
```bash
# Cluster-wide debugging
kubectl cluster-info dump > cluster-dump.yaml

# Check all resources
kubectl get all -A

# Check events
kubectl get events --sort-by=.metadata.creationTimestamp

# Check logs
kubectl logs -n kube-system -l k8s-app=kube-dns
kubectl logs -n kube-system -l app=flannel
```

#### **Network Debugging**
```bash
# Test DNS resolution
kubectl run test-dns --image=busybox --rm -it -- nslookup kubernetes.default

# Test pod-to-pod connectivity
kubectl run test-pod --image=busybox --rm -it -- ping <pod-ip>

# Test service connectivity
kubectl run test-svc --image=busybox --rm -it -- wget -qO- <service-name>.<namespace>.svc.cluster.local
```

### **🚑 Emergency Procedures**

#### **Complete Infrastructure Reset**
```bash
# 1. Backup current state
./scripts/state-sync.sh sync-up

# 2. Destroy all resources
./scripts/infra-manager.sh destroy-sequential

# 3. Clear local cache
find infrastructure -name ".terragrunt-cache" -type d -exec rm -rf {} +

# 4. Redeploy
./scripts/infra-manager.sh deploy-sequential
```

#### **Kubernetes Cluster Recovery**
```bash
# 1. Backup workloads
kubectl get all -o yaml -A > cluster-backup.yaml

# 2. Destroy and recreate cluster
cd infrastructure/kubernetes
terragrunt destroy
terragrunt apply

# 3. Reconfigure kubectl
./scripts/kube-context.sh update

# 4. Restore workloads
kubectl apply -f cluster-backup.yaml
```

### **📞 Getting Help**

#### **Log Collection**
```bash
# Collect infrastructure logs
cd infrastructure/<module>
terragrunt apply 2>&1 | tee deployment.log

# Collect Kubernetes logs
kubectl cluster-info dump > k8s-cluster-dump.yaml
kubectl get events -A --sort-by=.metadata.creationTimestamp > k8s-events.log

# Collect system information
./scripts/infra-manager.sh status > infrastructure-status.log
./scripts/kube-context.sh info > kubernetes-info.log
```

#### **Useful Resources**
- **OCI Documentation**: https://docs.oracle.com/en-us/iaas/
- **Terraform OCI Provider**: https://registry.terraform.io/providers/oracle/oci/latest/docs
- **Kubernetes Documentation**: https://kubernetes.io/docs/
- **OKE Documentation**: https://docs.oracle.com/en-us/iaas/Content/ContEng/home.htm

### **🔧 Prevention Tips**

#### **Best Practices**
1. **Always plan before apply**: `terragrunt plan`
2. **Use sequential deployment**: `./scripts/infra-manager.sh deploy-sequential`
3. **Regular state backups**: `./scripts/state-sync.sh sync-up`
4. **Monitor resource limits**: Check OCI service limits
5. **Test in development**: Validate changes in dev environment first

#### **Monitoring Setup**
```bash
# Set up basic monitoring
kubectl apply -f https://raw.githubusercontent.com/kubernetes/dashboard/v2.7.0/aio/deploy/recommended.yaml

# Create monitoring namespace
kubectl create namespace monitoring

# Monitor resource usage
watch kubectl top nodes
watch kubectl top pods -A
```

#### **Regular Maintenance**
```bash
# Weekly tasks
./scripts/state-sync.sh sync-up
./scripts/infra-manager.sh status
kubectl get nodes
kubectl top nodes

# Monthly tasks
# Review and update Kubernetes version
# Check for security updates
# Review resource utilization and costs
```

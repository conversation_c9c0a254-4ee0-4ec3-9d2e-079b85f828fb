# Production Environment - Network Module Configuration

# Include the root terragrunt.hcl configuration
include "root" {
  path = find_in_parent_folders()
}

# Specify the Terraform module source
terraform {
  source = "../../../modules/network"
}

# Dependencies - none for network module as it's the foundation
dependencies {
  paths = []
}

# Production environment specific inputs for network module
inputs = {
  # Override environment name for production
  environment = "prod"
  
  # Production-specific network configuration
  vcn_cidr = "********/16"  # Different CIDR for production
  
  public_subnets = [
    {
      cidr                = "10.2.1.0/24"
      name                = "public-subnet-1"
      availability_domain = 1
    },
    {
      cidr                = "10.2.2.0/24"
      name                = "public-subnet-2"
      availability_domain = 2
    },
    {
      cidr                = "10.2.3.0/24"
      name                = "public-subnet-3"
      availability_domain = 3
    }
  ]
  
  private_subnets = [
    {
      cidr                = "10.2.10.0/24"
      name                = "private-subnet-1"
      availability_domain = 1
    },
    {
      cidr                = "*********/24"
      name                = "private-subnet-2"
      availability_domain = 2
    },
    {
      cidr                = "*********/24"
      name                = "private-subnet-3"
      availability_domain = 3
    }
  ]
  
  # Production-specific security rules (more restrictive SSH access)
  security_rules = {
    web = [
      {
        port        = 80
        protocol    = "TCP"
        source      = "0.0.0.0/0"
        description = "HTTP traffic"
      },
      {
        port        = 443
        protocol    = "TCP"
        source      = "0.0.0.0/0"
        description = "HTTPS traffic"
      }
    ]
    ssh = [
      {
        port        = 22
        protocol    = "TCP"
        source      = "YOUR_OFFICE_IP/32"  # Replace with your office IP
        description = "SSH access from office only"
      }
    ]
    database = [
      {
        port        = 5432
        protocol    = "TCP"
        source      = "********/16"
        description = "PostgreSQL access"
      }
    ]
    kubernetes = [
      {
        port        = 6443
        protocol    = "TCP"
        source      = "********/16"
        description = "Kubernetes API server"
      },
      {
        port        = 2379
        protocol    = "TCP"
        source      = "********/16"
        description = "etcd client communication"
      },
      {
        port        = 2380
        protocol    = "TCP"
        source      = "********/16"
        description = "etcd peer communication"
      },
      {
        port        = 10250
        protocol    = "TCP"
        source      = "********/16"
        description = "Kubelet API"
      },
      {
        port        = 10251
        protocol    = "TCP"
        source      = "********/16"
        description = "kube-scheduler"
      },
      {
        port        = 10252
        protocol    = "TCP"
        source      = "********/16"
        description = "kube-controller-manager"
      }
    ]
    application = [
      {
        port        = 8080
        protocol    = "TCP"
        source      = "********/16"
        description = "Application port 1"
      },
      {
        port        = 8443
        protocol    = "TCP"
        source      = "********/16"
        description = "Application port 2"
      },
      {
        port        = 9090
        protocol    = "TCP"
        source      = "********/16"
        description = "Monitoring/Prometheus"
      }
    ]
  }
}

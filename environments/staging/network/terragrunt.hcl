# Staging Environment - Network Module Configuration

# Include the root terragrunt.hcl configuration
include "root" {
  path = find_in_parent_folders()
}

# Specify the Terraform module source
terraform {
  source = "../../../modules/network"
}

# Dependencies - none for network module as it's the foundation
dependencies {
  paths = []
}

# Staging environment specific inputs for network module
inputs = {
  # Override environment name for staging
  environment = "staging"
  
  # Staging-specific network configuration
  vcn_cidr = "10.1.0.0/16"  # Different CIDR for staging
  
  public_subnets = [
    {
      cidr                = "10.1.1.0/24"
      name                = "public-subnet-1"
      availability_domain = 1
    },
    {
      cidr                = "10.1.2.0/24"
      name                = "public-subnet-2"
      availability_domain = 2
    },
    {
      cidr                = "10.1.3.0/24"
      name                = "public-subnet-3"
      availability_domain = 3
    }
  ]
  
  private_subnets = [
    {
      cidr                = "10.1.10.0/24"
      name                = "private-subnet-1"
      availability_domain = 1
    },
    {
      cidr                = "10.1.11.0/24"
      name                = "private-subnet-2"
      availability_domain = 2
    },
    {
      cidr                = "10.1.12.0/24"
      name                = "private-subnet-3"
      availability_domain = 3
    }
  ]
}

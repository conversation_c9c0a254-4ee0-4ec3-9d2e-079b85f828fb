# Cluster Autoscaler Module Outputs

output "namespace" {
  description = "Kubernetes namespace where cluster autoscaler is deployed"
  value       = kubernetes_namespace.cluster_autoscaler.metadata[0].name
}

output "service_account_name" {
  description = "Name of the service account used by cluster autoscaler"
  value       = kubernetes_service_account.cluster_autoscaler.metadata[0].name
}

output "deployment_name" {
  description = "Name of the cluster autoscaler deployment"
  value       = kubernetes_deployment.cluster_autoscaler.metadata[0].name
}

output "cluster_role_name" {
  description = "Name of the cluster role for cluster autoscaler"
  value       = kubernetes_cluster_role.cluster_autoscaler.metadata[0].name
}

output "autoscaler_summary" {
  description = "Summary of cluster autoscaler configuration"
  value = {
    namespace           = kubernetes_namespace.cluster_autoscaler.metadata[0].name
    deployment_name     = kubernetes_deployment.cluster_autoscaler.metadata[0].name
    service_account     = kubernetes_service_account.cluster_autoscaler.metadata[0].name
    node_pool_id        = var.node_pool_id
    min_nodes          = var.min_nodes
    max_nodes          = var.max_nodes
    autoscaler_image   = var.autoscaler_image
  }
}

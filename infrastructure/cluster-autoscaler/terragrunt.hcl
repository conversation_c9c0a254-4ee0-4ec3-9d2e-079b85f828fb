# Cluster Autoscaler Module Configuration

# Include the root terragrunt.hcl configuration
include "root" {
  path = find_in_parent_folders()
}

# Specify the Terraform module source
terraform {
  source = "../../modules/cluster-autoscaler"
}

# Dependencies - Cluster autoscaler depends on Kubernetes cluster
dependency "kubernetes" {
  config_path = "../kubernetes"
  
  mock_outputs = {
    node_pool_id = "ocid1.nodepool.oc1..mock"
  }
}

# Cluster autoscaler module inputs
inputs = {
  # Dependencies
  node_pool_id = dependency.kubernetes.outputs.node_pool_id
  
  # Autoscaler configuration
  namespace        = "kube-system"
  autoscaler_image = "k8s.gcr.io/autoscaling/cluster-autoscaler:v1.28.2"
  
  # Scaling configuration
  min_nodes                    = 1
  max_nodes                    = 10
  max_node_provision_time      = "15m"
  scale_down_delay_after_add   = "10m"
  scale_down_unneeded_time     = "10m"
  scale_down_util_threshold    = "0.5"
  skip_nodes_with_local_storage = false
  skip_nodes_with_system_pods   = true
  
  # Resource configuration
  resources = {
    limits = {
      cpu    = "100m"
      memory = "300Mi"
    }
    requests = {
      cpu    = "100m"
      memory = "300Mi"
    }
  }
}

# Cluster Autoscaler Module Configuration

# Include the root configuration
include "root" {
  path = find_in_parent_folders("root.hcl")
}

# Load common variables
locals {
  common_vars = yamldecode(file(find_in_parent_folders("common-vars.yaml")))
  env_vars    = local.common_vars.environment
  oci_vars    = local.common_vars.oci
  k8s_vars    = local.common_vars.kubernetes
  shared_vars = local.common_vars.shared
}

# Specify the Terraform module source
terraform {
  source = "../../modules/cluster-autoscaler"
}

# Dependencies - Cluster autoscaler depends on Kubernetes cluster
dependency "kubernetes" {
  config_path = "../kubernetes"
  
  mock_outputs = {
    cluster_id                  = "mock-cluster-id"
    cluster_name               = "mock-cluster"
    cluster_endpoint           = "https://mock-endpoint"
    cluster_ca_certificate     = "mock-ca-cert"
    kubeconfig                 = "mock-kubeconfig"
    node_pool_id               = "ocid1.nodepool.oc1..mock"
    node_pool_ids              = { workers = "mock-node-pool-id" }
  }
}

# Cluster autoscaler module inputs
inputs = {
  # Basic configuration
  compartment_ocid = local.oci_vars.compartment_ocid
  oci_region       = local.oci_vars.region

  # Dependencies from Kubernetes cluster
  node_pool_id = dependency.kubernetes.outputs.node_pool_id

  # Autoscaler configuration from common vars
  namespace        = local.k8s_vars.autoscaler.namespace
  autoscaler_image = local.k8s_vars.autoscaler.image

  # Scaling configuration from common vars
  min_nodes                     = local.k8s_vars.autoscaler.min_nodes
  max_nodes                     = local.k8s_vars.autoscaler.max_nodes
  max_node_provision_time       = local.k8s_vars.autoscaler.max_node_provision_time
  scale_down_delay_after_add    = local.k8s_vars.autoscaler.scale_down_delay_after_add
  scale_down_unneeded_time      = local.k8s_vars.autoscaler.scale_down_unneeded_time
  scale_down_util_threshold     = local.k8s_vars.autoscaler.scale_down_util_threshold
  skip_nodes_with_local_storage = local.k8s_vars.autoscaler.skip_nodes_with_local_storage
  skip_nodes_with_system_pods   = local.k8s_vars.autoscaler.skip_nodes_with_system_pods

  # Resource configuration from common vars
  resources = local.k8s_vars.autoscaler.resources

  # Common tags from common vars
  common_tags = local.env_vars.common_tags
}

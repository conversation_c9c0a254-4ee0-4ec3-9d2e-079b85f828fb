# Cluster Autoscaler Module Configuration

# Include the root terragrunt.hcl configuration
include "root" {
  path = find_in_parent_folders()
}

# Load common variables
locals {
  common_vars = yamldecode(file(find_in_parent_folders("common-vars.yaml")))
  env_vars    = local.common_vars.environments.dev
  k8s_vars    = local.env_vars.kubernetes
}

# Specify the Terraform module source
terraform {
  source = "../../modules/cluster-autoscaler"
}

# Dependencies - Cluster autoscaler depends on Kubernetes cluster
dependency "kubernetes" {
  config_path = "../kubernetes"
  
  mock_outputs = {
    node_pool_id = "ocid1.nodepool.oc1..mock"
  }
}

# Cluster autoscaler module inputs
inputs = {
  # Dependencies
  node_pool_id = dependency.kubernetes.outputs.node_pool_id

  # OCI configuration (inherited from root)
  oci_region = "us-phoenix-1"

  # Autoscaler configuration from common vars
  namespace        = local.k8s_vars.autoscaler.namespace
  autoscaler_image = local.k8s_vars.autoscaler.image

  # Scaling configuration from common vars
  min_nodes                     = local.k8s_vars.autoscaler.min_nodes
  max_nodes                     = local.k8s_vars.autoscaler.max_nodes
  max_node_provision_time       = local.k8s_vars.autoscaler.max_node_provision_time
  scale_down_delay_after_add    = local.k8s_vars.autoscaler.scale_down_delay_after_add
  scale_down_unneeded_time      = local.k8s_vars.autoscaler.scale_down_unneeded_time
  scale_down_util_threshold     = local.k8s_vars.autoscaler.scale_down_util_threshold
  skip_nodes_with_local_storage = local.k8s_vars.autoscaler.skip_nodes_with_local_storage
  skip_nodes_with_system_pods   = local.k8s_vars.autoscaler.skip_nodes_with_system_pods
  
  # Resource configuration from common vars
  resources = local.k8s_vars.autoscaler.resources

  # Common tags from common vars
  common_tags = local.env_vars.common_tags
}

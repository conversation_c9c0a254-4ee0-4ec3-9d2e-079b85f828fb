# Kubernetes Module Configuration

# Include the root configuration
include "root" {
  path = find_in_parent_folders("root.hcl")
}

# Load common variables for module-specific configuration
locals {
  common_vars = yamldecode(file(find_in_parent_folders("common-vars.yaml")))
  env_vars    = local.common_vars.environment
  k8s_vars    = local.common_vars.kubernetes
  shared_vars = local.common_vars.shared
}

# Specify the Terraform module source
terraform {
  source = "../../modules/kubernetes"
}

# Dependencies - Kubernetes depends on network
dependency "network" {
  config_path = "../network"

  mock_outputs = {
    vcn_id             = "mock-vcn-id"
    private_subnet_ids = ["mock-subnet-1", "mock-subnet-2", "mock-subnet-3"]
    public_subnet_ids  = ["mock-subnet-1", "mock-subnet-2", "mock-subnet-3"]
    oke_regional_subnet_id = "mock-oke-subnet"
  }
}

# Kubernetes module inputs
inputs = {
  # Network dependencies from dependency block
  vcn_id             = dependency.network.outputs.vcn_id
  public_subnet_ids  = dependency.network.outputs.public_subnet_ids
  private_subnet_ids = dependency.network.outputs.private_subnet_ids

  # Kubernetes cluster configuration from common vars
  cluster_name       = local.k8s_vars.cluster_name != "" ? local.k8s_vars.cluster_name : "${local.env_vars.project_name}-${local.env_vars.name}-oke"
  kubernetes_version = local.k8s_vars.kubernetes_version
  api_access_cidr    = local.k8s_vars.api_access_cidr

  # Cluster endpoint configuration
  cluster_endpoint_config = {
    is_public_ip_enabled = true
    subnet_id           = dependency.network.outputs.oke_regional_subnet_id
    nsg_ids             = []
  }

  # Node pools configuration from common vars (convert map to list)
  node_pools = [
    for pool_name, pool_config in local.k8s_vars.node_pools : {
      name                      = pool_name
      enabled                   = true
      node_shape                = pool_config.node_shape
      node_shape_memory_gb      = pool_config.node_shape_memory_gb
      node_shape_ocpus          = pool_config.node_shape_ocpus
      initial_node_labels       = pool_config.initial_node_labels
      size                      = pool_config.size
      max_size                  = pool_config.max_size
      min_size                  = pool_config.min_size
      boot_volume_size_gb       = pool_config.boot_volume_size_gb
      image_id                  = pool_config.image_id
    }
  ]

  # SSH configuration (from shared vars)
  ssh_public_key = local.shared_vars.ssh_public_key

  # Service configuration
  services_cidr = "10.96.0.0/16"
  pods_cidr     = "10.244.0.0/16"

  # Load balancer configuration
  load_balancer_config = {
    load_balancer_type    = "lb"
    service_lb_subnet_ids = dependency.network.outputs.public_subnet_ids
  }

  # Common tags from common vars
  common_tags = local.env_vars.common_tags
}

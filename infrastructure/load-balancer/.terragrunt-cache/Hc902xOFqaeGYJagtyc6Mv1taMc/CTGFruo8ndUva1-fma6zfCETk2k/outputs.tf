# Load Balancer Module Outputs

output "load_balancer_id" {
  description = "The OCID of the load balancer"
  value       = oci_load_balancer_load_balancer.main.id
}

output "load_balancer_ip_addresses" {
  description = "IP addresses of the load balancer"
  value       = oci_load_balancer_load_balancer.main.ip_address_details
}

output "load_balancer_hostname" {
  description = "Hostname of the load balancer"
  value       = oci_load_balancer_load_balancer.main.ip_address_details[0].ip_address
}

output "backend_set_names" {
  description = "Names of the backend sets"
  value       = keys(oci_load_balancer_backend_set.main)
}

output "listener_names" {
  description = "Names of the listeners"
  value       = keys(oci_load_balancer_listener.main)
}

output "certificate_names" {
  description = "Names of the certificates"
  value       = keys(oci_load_balancer_certificate.main)
}

output "load_balancer_summary" {
  description = "Summary of load balancer configuration"
  value = {
    load_balancer_id = oci_load_balancer_load_balancer.main.id
    display_name     = oci_load_balancer_load_balancer.main.display_name
    shape           = oci_load_balancer_load_balancer.main.shape
    is_private      = oci_load_balancer_load_balancer.main.is_private
    ip_addresses    = oci_load_balancer_load_balancer.main.ip_address_details
    backend_sets    = keys(oci_load_balancer_backend_set.main)
    listeners       = keys(oci_load_balancer_listener.main)
    state           = oci_load_balancer_load_balancer.main.state
  }
}

# Load Balancer Module Configuration

# Include the root terragrunt.hcl configuration
include "root" {
  path = find_in_parent_folders()
}

# Specify the Terraform module source
terraform {
  source = "../../modules/load-balancer"
}

# Dependencies - Load balancer depends on network
dependency "network" {
  config_path = "../network"
  
  mock_outputs = {
    public_subnet_ids = ["ocid1.subnet.oc1..mock1", "ocid1.subnet.oc1..mock2"]
  }
}

# Load balancer module inputs
inputs = {
  # Network dependencies
  subnet_ids = dependency.network.outputs.public_subnet_ids
  
  # Load balancer configuration
  load_balancer_shape = "flexible"
  shape_details = {
    maximum_bandwidth_in_mbps = 100
    minimum_bandwidth_in_mbps = 10
  }
  is_private                 = false
  network_security_group_ids = []
  
  # Backend sets configuration
  backend_sets = {
    "web-backend" = {
      policy = "ROUND_ROBIN"
      health_checker = {
        protocol          = "HTTP"
        port              = 80
        url_path          = "/health"
        return_code       = 200
        interval_ms       = 10000
        timeout_in_millis = 3000
        retries           = 3
      }
      backends = [
        # Add your backend servers here
        # {
        #   ip_address = "**********"
        #   port       = 80
        #   backup     = false
        #   drain      = false
        #   offline    = false
        #   weight     = 1
        # }
      ]
      session_persistence_enabled          = false
      session_persistence_cookie_name      = ""
      session_persistence_disable_fallback = false
      ssl_configuration                    = null
    }
  }
  
  # Listeners configuration
  listeners = {
    "http-listener" = {
      default_backend_set_name = "web-backend"
      port                     = 80
      protocol                 = "HTTP"
      ssl_configuration        = null
      connection_configuration = {
        idle_timeout_in_seconds = 300
      }
    }
    # Uncomment for HTTPS listener
    # "https-listener" = {
    #   default_backend_set_name = "web-backend"
    #   port                     = 443
    #   protocol                 = "HTTP"
    #   ssl_configuration = {
    #     certificate_name        = "ssl-cert"
    #     verify_depth           = 5
    #     verify_peer_certificate = false
    #   }
    #   connection_configuration = {
    #     idle_timeout_in_seconds = 300
    #   }
    # }
  }
  
  # SSL certificates (if needed)
  certificates = {
    # "ssl-cert" = {
    #   ca_certificate     = file("path/to/ca.crt")
    #   private_key        = file("path/to/private.key")
    #   public_certificate = file("path/to/public.crt")
    #   passphrase         = ""
    # }
  }
  
  # Path route sets for URL-based routing
  path_route_sets = {
    # "api-routes" = {
    #   path_routes = [
    #     {
    #       backend_set_name = "api-backend"
    #       path             = "/api/*"
    #       path_match_type  = "PREFIX_MATCH"
    #     }
    #   ]
    # }
  }
}

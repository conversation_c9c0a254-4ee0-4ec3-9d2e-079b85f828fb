# Network Module Configuration

# Include the root configuration
include "root" {
  path = find_in_parent_folders("root.hcl")
}

# Specify the Terraform module source
terraform {
  source = "../../modules/network"
}

# Dependencies - none for network module as it's the foundation
dependencies {
  paths = []
}

# Network module inputs
inputs = {
  # All common inputs are inherited from root terragrunt.hcl
  # Add any network-specific overrides here if needed
}

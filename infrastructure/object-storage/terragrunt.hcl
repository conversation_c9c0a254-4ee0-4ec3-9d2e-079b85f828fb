# Object Storage Module Configuration

# Include the root configuration
include "root" {
  path = find_in_parent_folders("root.hcl")
}

# Load common variables for module-specific configuration
locals {
  common_vars = yamldecode(file(find_in_parent_folders("common-vars.yaml")))
  env_vars    = local.common_vars.environment
  oci_vars    = local.common_vars.oci
  storage_vars = local.common_vars.object_storage
}

# Specify the Terraform module source
terraform {
  source = "../../modules/object-storage"
}

# Object Storage module inputs
inputs = {
  # Basic configuration
  compartment_ocid = local.oci_vars.compartment_ocid
  environment      = local.env_vars.name
  project_name     = local.env_vars.project_name

  # Bucket configuration from common vars
  bucket_name            = local.storage_vars.bucket_name
  storage_tier           = local.storage_vars.storage_tier
  object_events_enabled  = local.storage_vars.object_events_enabled

  # Common tags from common vars
  common_tags = local.env_vars.common_tags
}

# Kubernetes Module Configuration

# Load common variables
locals {
  common_vars = yamldecode(file(find_in_parent_folders("common-vars.yaml")))
  env_vars    = local.common_vars.environment
  k8s_vars    = local.common_vars.kubernetes
}

# Generate provider configuration
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOT
provider "oci" {
  tenancy_ocid     = "${local.common_vars.oci.tenancy_ocid}"
  user_ocid        = "${local.common_vars.oci.user_ocid}"
  fingerprint      = "${local.common_vars.oci.fingerprint}"
  private_key_path = "${local.common_vars.oci.private_key_path}"
  region           = "${local.common_vars.oci.region}"
}
EOT
}

# Specify the Terraform module source
terraform {
  source = "../../modules/kubernetes"
}

# Kubernetes module inputs
inputs = {
  # Basic OCI configuration
  compartment_ocid = local.common_vars.oci.compartment_ocid
  environment      = local.env_vars.name
  project_name     = local.env_vars.project_name

  # Network configuration (hardcoded from network outputs)
  vcn_id             = "ocid1.vcn.oc1.phx.amaaaaaancu2jkyap7zotu6kep6zeownehef3attwljdvfldtfgfhufsuumq"
  public_subnet_ids  = [
    "ocid1.subnet.oc1.phx.aaaaaaaa5otnf5rq45v4dz2tgwrkcccs2lrw3mu2652tj6ymu24fc7gamo2q",
    "ocid1.subnet.oc1.phx.aaaaaaaaym2godie7g3mhak6ours3pdoyrsx3mvp7cfuo4jo5y3igi2hpewa",
    "ocid1.subnet.oc1.phx.aaaaaaaatqhznclrae3xdzmjt5ak67kvvyzas3rtx6cwqd56j5nwdxswns7a"
  ]
  private_subnet_ids = [
    "ocid1.subnet.oc1.phx.aaaaaaaa54skvjyzsnfmmanwm3ibh65ief4uvjkuowza5jc3m5e2wkwkycga",
    "ocid1.subnet.oc1.phx.aaaaaaaahvzhfjrd5g32z7owtxv6c5v3zdx6drl2wr5cykffhfv4t4oxf4zq",
    "ocid1.subnet.oc1.phx.aaaaaaaaiuw277wantfrqqmb3gxptidds74qeboqbqmzpl4lhhloi7zoad2q"
  ]

  # Kubernetes cluster configuration from common vars
  cluster_name       = local.k8s_vars.cluster_name != "" ? local.k8s_vars.cluster_name : "${local.env_vars.project_name}-${local.env_vars.name}-oke"
  kubernetes_version = local.k8s_vars.kubernetes_version
  api_access_cidr    = local.k8s_vars.api_access_cidr

  # Cluster endpoint configuration
  cluster_endpoint_config = {
    is_public_ip_enabled = true
    subnet_id           = "ocid1.subnet.oc1.phx.aaaaaaaayfcgmwzgojg2jc4lczwxtx7jcg4vrhl25twlubem2q3ae4pbvtxq"
  }

  # Node pools configuration from common vars
  node_pools = local.k8s_vars.node_pools

  # SSH configuration
  ssh_public_key = local.k8s_vars.ssh_public_key

  # Service configuration
  services_cidr = "*********/16"
  pods_cidr     = "**********/16"

  # Load balancer configuration
  load_balancer_config = {
    load_balancer_type    = "lb"
    service_lb_subnet_ids = [
      "ocid1.subnet.oc1.phx.aaaaaaaa5otnf5rq45v4dz2tgwrkcccs2lrw3mu2652tj6ymu24fc7gamo2q",
      "ocid1.subnet.oc1.phx.aaaaaaaaym2godie7g3mhak6ours3pdoyrsx3mvp7cfuo4jo5y3igi2hpewa"
    ]
  }

  # Common tags from common vars
  common_tags = local.env_vars.common_tags
}

# Cluster Autoscaler Module Outputs

output "namespace" {
  description = "Kubernetes namespace where cluster autoscaler is deployed"
  value       = var.enabled ? data.kubernetes_namespace.cluster_autoscaler[0].metadata[0].name : null
}

output "service_account_name" {
  description = "Name of the service account used by cluster autoscaler"
  value       = var.enabled ? kubernetes_service_account.cluster_autoscaler[0].metadata[0].name : null
}

output "deployment_name" {
  description = "Name of the cluster autoscaler deployment"
  value       = var.enabled ? kubernetes_deployment.cluster_autoscaler[0].metadata[0].name : null
}

output "cluster_role_name" {
  description = "Name of the cluster role for cluster autoscaler"
  value       = var.enabled ? kubernetes_cluster_role.cluster_autoscaler[0].metadata[0].name : null
}

output "autoscaler_summary" {
  description = "Summary of cluster autoscaler configuration"
  value = var.enabled ? {
    namespace           = data.kubernetes_namespace.cluster_autoscaler[0].metadata[0].name
    deployment_name     = kubernetes_deployment.cluster_autoscaler[0].metadata[0].name
    service_account     = kubernetes_service_account.cluster_autoscaler[0].metadata[0].name
    node_pool_id        = var.node_pool_id
    min_nodes          = var.min_nodes
    max_nodes          = var.max_nodes
    autoscaler_image   = var.autoscaler_image
  } : {
    enabled = false
    message = "Cluster autoscaler is disabled"
  }
}

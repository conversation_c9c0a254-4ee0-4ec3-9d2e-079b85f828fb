# Cluster Autoscaler Module Variables

variable "compartment_ocid" {
  description = "The OCID of the compartment where resources will be created"
  type        = string
}

variable "oci_region" {
  description = "OCI region"
  type        = string
}

variable "node_pool_id" {
  description = "The OCID of the node pool to autoscale"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace for cluster autoscaler"
  type        = string
  default     = "kube-system"
}

variable "autoscaler_image" {
  description = "Docker image for cluster autoscaler"
  type        = string
  default     = "k8s.gcr.io/autoscaling/cluster-autoscaler:v1.21.0"
}

variable "min_nodes" {
  description = "Minimum number of nodes in the node pool"
  type        = number
  default     = 1
}

variable "max_nodes" {
  description = "Maximum number of nodes in the node pool"
  type        = number
  default     = 10
}

variable "max_node_provision_time" {
  description = "Maximum time to wait for node provisioning"
  type        = string
  default     = "15m"
}

variable "scale_down_delay_after_add" {
  description = "How long after scale up that scale down evaluation resumes"
  type        = string
  default     = "10m"
}

variable "scale_down_unneeded_time" {
  description = "How long a node should be unneeded before it is eligible for scale down"
  type        = string
  default     = "10m"
}

variable "scale_down_util_threshold" {
  description = "Node utilization level below which a node can be considered for scale down"
  type        = string
  default     = "0.5"
}

variable "skip_nodes_with_local_storage" {
  description = "If true cluster autoscaler will never delete nodes with pods with local storage"
  type        = bool
  default     = false
}

variable "skip_nodes_with_system_pods" {
  description = "If true cluster autoscaler will never delete nodes with pods from kube-system"
  type        = bool
  default     = true
}

variable "resources" {
  description = "Resource requests and limits for cluster autoscaler"
  type = object({
    limits = object({
      cpu    = string
      memory = string
    })
    requests = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    limits = {
      cpu    = "100m"
      memory = "300Mi"
    }
    requests = {
      cpu    = "100m"
      memory = "300Mi"
    }
  }
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

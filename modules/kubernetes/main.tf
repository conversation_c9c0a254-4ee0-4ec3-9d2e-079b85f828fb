# Kubernetes (OKE) Module - Main Configuration

# Data source to get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_ocid
}

# Data source for OKE node pool options
data "oci_containerengine_node_pool_option" "oke_node_pool_option" {
  node_pool_option_id = "all"
}

# Data source for OKE cluster options
data "oci_containerengine_cluster_option" "oke_cluster_option" {
  cluster_option_id = "all"
}

locals {
  cluster_name = var.cluster_name != "" ? var.cluster_name : "${var.project_name}-${var.environment}-oke"
}

# Create OKE Cluster
resource "oci_containerengine_cluster" "oke_cluster" {
  compartment_id     = var.compartment_ocid
  kubernetes_version = var.kubernetes_version
  name               = local.cluster_name
  vcn_id             = var.vcn_id

  endpoint_config {
    is_public_ip_enabled = var.cluster_endpoint_config.is_public_ip_enabled
    subnet_id           = var.cluster_endpoint_config.subnet_id
    nsg_ids             = var.cluster_endpoint_config.nsg_ids
  }

  options {
    service_lb_subnet_ids = var.cluster_options.service_lb_subnet_ids

    add_ons {
      is_kubernetes_dashboard_enabled = var.cluster_options.add_ons.is_kubernetes_dashboard_enabled
      is_tiller_enabled              = var.cluster_options.add_ons.is_tiller_enabled
    }

    admission_controller_options {
      is_pod_security_policy_enabled = var.cluster_options.admission_controller_options.is_pod_security_policy_enabled
    }

    kubernetes_network_config {
      pods_cidr     = var.cluster_options.kubernetes_network_config.pods_cidr
      services_cidr = var.cluster_options.kubernetes_network_config.services_cidr
    }
  }

  freeform_tags = merge(var.common_tags, {
    Name = local.cluster_name
    Type = "kubernetes-cluster"
  })
}

# Create Node Pool
# Create multiple node pools
resource "oci_containerengine_node_pool" "oke_node_pools" {
  for_each = {
    for pool in var.node_pools : pool.name => pool
    if pool.enabled
  }

  cluster_id         = oci_containerengine_cluster.oke_cluster.id
  compartment_id     = var.compartment_ocid
  kubernetes_version = var.kubernetes_version
  name               = "${var.project_name}-${var.environment}-${each.value.name}"

  node_config_details {
    dynamic "placement_configs" {
      for_each = var.private_subnet_ids
      content {
        availability_domain = data.oci_identity_availability_domains.ads.availability_domains[placement_configs.key % length(data.oci_identity_availability_domains.ads.availability_domains)].name
        subnet_id          = placement_configs.value
      }
    }

    size = each.value.size
  }

  node_shape = each.value.node_shape

  dynamic "node_shape_config" {
    for_each = each.value.node_shape_memory_gb > 0 ? [1] : []
    content {
      memory_in_gbs = each.value.node_shape_memory_gb
      ocpus        = each.value.node_shape_ocpus
    }
  }

  node_source_details {
    image_id    = each.value.image_id != "" ? each.value.image_id : data.oci_containerengine_node_pool_option.oke_node_pool_option.sources[0].image_id
    source_type = "IMAGE"

    boot_volume_size_in_gbs = each.value.boot_volume_size_gb
  }

  dynamic "initial_node_labels" {
    for_each = each.value.initial_node_labels
    content {
      key   = initial_node_labels.key
      value = initial_node_labels.value
    }
  }

  ssh_public_key = var.ssh_public_key

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-${each.value.name}"
    Type = "kubernetes-node-pool"
  })
}

# Create Network Security Group for OKE cluster
resource "oci_core_network_security_group" "oke_cluster_nsg" {
  compartment_id = var.compartment_ocid
  vcn_id         = var.vcn_id
  display_name   = "${var.project_name}-${var.environment}-oke-cluster-nsg"

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-oke-cluster-nsg"
    Type = "network-security-group"
  })
}

# NSG rule for Kubernetes API access
resource "oci_core_network_security_group_security_rule" "oke_api_access" {
  network_security_group_id = oci_core_network_security_group.oke_cluster_nsg.id
  direction                 = "INGRESS"
  protocol                  = "6" # TCP

  description = "Allow Kubernetes API access"
  source      = var.api_access_cidr
  source_type = "CIDR_BLOCK"

  tcp_options {
    destination_port_range {
      min = 6443
      max = 6443
    }
  }
}

# NSG rule for node communication
resource "oci_core_network_security_group_security_rule" "oke_node_communication" {
  network_security_group_id = oci_core_network_security_group.oke_cluster_nsg.id
  direction                 = "INGRESS"
  protocol                  = "all"

  description = "Allow communication between nodes"
  source      = oci_core_network_security_group.oke_cluster_nsg.id
  source_type = "NETWORK_SECURITY_GROUP"
}

# Data source for kubeconfig
data "oci_containerengine_cluster_kube_config" "oke_cluster_kube_config" {
  cluster_id    = oci_containerengine_cluster.oke_cluster.id
  token_version = "2.0.0"
  endpoint      = "PUBLIC_ENDPOINT"
}

# Example of what the actual implementation might look like:
#
# resource "oci_containerengine_cluster" "oke_cluster" {
#   compartment_id     = var.compartment_ocid
#   kubernetes_version = var.kubernetes_version
#   name               = var.cluster_name != "" ? var.cluster_name : "${var.project_name}-${var.environment}-oke"
#   vcn_id             = var.vcn_id
#   
#   endpoint_config {
#     is_public_ip_enabled = true
#     subnet_id           = var.public_subnet_ids[0]
#   }
#   
#   options {
#     service_lb_subnet_ids = var.cluster_options.service_lb_subnet_ids
#     
#     add_ons {
#       is_kubernetes_dashboard_enabled = var.cluster_options.add_ons.is_kubernetes_dashboard_enabled
#       is_tiller_enabled              = var.cluster_options.add_ons.is_tiller_enabled
#     }
#     
#     admission_controller_options {
#       is_pod_security_policy_enabled = var.cluster_options.admission_controller_options.is_pod_security_policy_enabled
#     }
#     
#     kubernetes_network_config {
#       pods_cidr     = var.cluster_options.kubernetes_network_config.pods_cidr
#       services_cidr = var.cluster_options.kubernetes_network_config.services_cidr
#     }
#   }
#   
#   freeform_tags = merge(var.common_tags, {
#     Name = var.cluster_name != "" ? var.cluster_name : "${var.project_name}-${var.environment}-oke"
#     Type = "kubernetes-cluster"
#   })
# }
#
# resource "oci_containerengine_node_pool" "oke_node_pool" {
#   cluster_id         = oci_containerengine_cluster.oke_cluster.id
#   compartment_id     = var.compartment_ocid
#   kubernetes_version = var.kubernetes_version
#   name               = "${var.project_name}-${var.environment}-node-pool"
#   
#   node_config_details {
#     placement_configs {
#       availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
#       subnet_id          = var.private_subnet_ids[0]
#     }
#     placement_configs {
#       availability_domain = data.oci_identity_availability_domains.ads.availability_domains[1].name
#       subnet_id          = var.private_subnet_ids[1]
#     }
#     placement_configs {
#       availability_domain = data.oci_identity_availability_domains.ads.availability_domains[2].name
#       subnet_id          = var.private_subnet_ids[2]
#     }
#     
#     size = var.node_pool_config.size
#   }
#   
#   node_shape = var.node_pool_config.node_shape
#   
#   node_shape_config {
#     memory_in_gbs = var.node_pool_config.node_shape_memory_gb
#     ocpus        = var.node_pool_config.node_shape_ocpus
#   }
#   
#   node_source_details {
#     image_id    = data.oci_containerengine_node_pool_option.oke_node_pool_option.sources[0].image_id
#     source_type = "IMAGE"
#     
#     boot_volume_size_in_gbs = var.node_pool_config.boot_volume_size_gb
#   }
#   
#   initial_node_labels {
#     key   = "environment"
#     value = var.environment
#   }
#   
#   ssh_public_key = var.ssh_public_key
#   
#   freeform_tags = merge(var.common_tags, {
#     Name = "${var.project_name}-${var.environment}-node-pool"
#     Type = "kubernetes-node-pool"
#   })
# }

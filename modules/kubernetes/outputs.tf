# Kubernetes (OKE) Module Outputs

output "cluster_id" {
  description = "The OCID of the OKE cluster"
  value       = oci_containerengine_cluster.oke_cluster.id
}

output "cluster_name" {
  description = "The name of the OKE cluster"
  value       = oci_containerengine_cluster.oke_cluster.name
}

output "cluster_endpoint" {
  description = "The endpoint of the OKE cluster"
  value       = oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint
}

output "kubeconfig" {
  description = "Kubeconfig for the OKE cluster"
  value       = data.oci_containerengine_cluster_kube_config.oke_cluster_kube_config.content
  sensitive   = true
}

output "node_pool_id" {
  description = "The OCID of the node pool"
  value       = oci_containerengine_node_pool.oke_node_pool.id
}

output "node_pool_name" {
  description = "The name of the node pool"
  value       = oci_containerengine_node_pool.oke_node_pool.name
}

output "cluster_ca_certificate" {
  description = "The CA certificate of the OKE cluster"
  value       = base64decode(oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint_ca_certificate)
  sensitive   = true
}

output "network_security_group_id" {
  description = "OCID of the network security group for OKE cluster"
  value       = oci_core_network_security_group.oke_cluster_nsg.id
}

output "cluster_summary" {
  description = "Summary of cluster configuration"
  value = {
    cluster_id       = oci_containerengine_cluster.oke_cluster.id
    cluster_name     = oci_containerengine_cluster.oke_cluster.name
    endpoint         = oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint
    kubernetes_version = var.kubernetes_version
    node_pool_id     = oci_containerengine_node_pool.oke_node_pool.id
    node_pool_size   = var.node_pool_config.size
    node_shape       = var.node_pool_config.node_shape
  }
}

# Example of what the actual outputs might look like:
#
# output "cluster_id" {
#   description = "The OCID of the OKE cluster"
#   value       = oci_containerengine_cluster.oke_cluster.id
# }
#
# output "cluster_name" {
#   description = "The name of the OKE cluster"
#   value       = oci_containerengine_cluster.oke_cluster.name
# }
#
# output "cluster_endpoint" {
#   description = "The endpoint of the OKE cluster"
#   value       = oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint
# }
#
# output "kubeconfig" {
#   description = "Kubeconfig for the OKE cluster"
#   value       = data.oci_containerengine_cluster_kube_config.oke_cluster_kube_config.content
#   sensitive   = true
# }
#
# output "node_pool_id" {
#   description = "The OCID of the node pool"
#   value       = oci_containerengine_node_pool.oke_node_pool.id
# }
#
# output "node_pool_name" {
#   description = "The name of the node pool"
#   value       = oci_containerengine_node_pool.oke_node_pool.name
# }
#
# output "cluster_ca_certificate" {
#   description = "The CA certificate of the OKE cluster"
#   value       = base64decode(oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint_ca_certificate)
#   sensitive   = true
# }

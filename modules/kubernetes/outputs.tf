# Kubernetes (OKE) Module Outputs

output "cluster_id" {
  description = "The OCID of the OKE cluster"
  value       = oci_containerengine_cluster.oke_cluster.id
}

output "cluster_name" {
  description = "The name of the OKE cluster"
  value       = oci_containerengine_cluster.oke_cluster.name
}

output "cluster_endpoint" {
  description = "The endpoint of the OKE cluster"
  value       = oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint
}

output "kubeconfig" {
  description = "Kubeconfig for the OKE cluster"
  value       = data.oci_containerengine_cluster_kube_config.oke_cluster_kube_config.content
  sensitive   = true
}

output "node_pool_ids" {
  description = "The OCIDs of all node pools"
  value       = { for k, v in oci_containerengine_node_pool.oke_node_pools : k => v.id }
}

output "node_pool_names" {
  description = "The names of all node pools"
  value       = { for k, v in oci_containerengine_node_pool.oke_node_pools : k => v.name }
}

# Backward compatibility - return the first node pool ID
output "node_pool_id" {
  description = "The OCID of the first node pool (for backward compatibility)"
  value       = length(oci_containerengine_node_pool.oke_node_pools) > 0 ? values(oci_containerengine_node_pool.oke_node_pools)[0].id : null
}

output "node_pool_name" {
  description = "The name of the first node pool (for backward compatibility)"
  value       = length(oci_containerengine_node_pool.oke_node_pools) > 0 ? values(oci_containerengine_node_pool.oke_node_pools)[0].name : null
}

output "cluster_ca_certificate" {
  description = "The CA certificate of the OKE cluster"
  value       = length(oci_containerengine_cluster.oke_cluster.endpoints) > 0 ? oci_containerengine_cluster.oke_cluster.endpoints[0] : null
  sensitive   = true
}

output "network_security_group_id" {
  description = "OCID of the network security group for OKE cluster"
  value       = oci_core_network_security_group.oke_cluster_nsg.id
}

output "cluster_summary" {
  description = "Summary of cluster configuration"
  value = {
    cluster_id         = oci_containerengine_cluster.oke_cluster.id
    cluster_name       = oci_containerengine_cluster.oke_cluster.name
    endpoint           = length(oci_containerengine_cluster.oke_cluster.endpoints) > 0 ? oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint : null
    kubernetes_version = var.kubernetes_version
    node_pools = {
      for k, v in oci_containerengine_node_pool.oke_node_pools : k => {
        id         = v.id
        name       = v.name
        size       = v.node_config_details[0].size
        node_shape = v.node_shape
      }
    }
    total_nodes = sum([for pool in oci_containerengine_node_pool.oke_node_pools : pool.node_config_details[0].size])
  }
}

# Example of what the actual outputs might look like:
#
# output "cluster_id" {
#   description = "The OCID of the OKE cluster"
#   value       = oci_containerengine_cluster.oke_cluster.id
# }
#
# output "cluster_name" {
#   description = "The name of the OKE cluster"
#   value       = oci_containerengine_cluster.oke_cluster.name
# }
#
# output "cluster_endpoint" {
#   description = "The endpoint of the OKE cluster"
#   value       = oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint
# }
#
# output "kubeconfig" {
#   description = "Kubeconfig for the OKE cluster"
#   value       = data.oci_containerengine_cluster_kube_config.oke_cluster_kube_config.content
#   sensitive   = true
# }
#
# output "node_pool_id" {
#   description = "The OCID of the node pool"
#   value       = oci_containerengine_node_pool.oke_node_pool.id
# }
#
# output "node_pool_name" {
#   description = "The name of the node pool"
#   value       = oci_containerengine_node_pool.oke_node_pool.name
# }
#
# output "cluster_ca_certificate" {
#   description = "The CA certificate of the OKE cluster"
#   value       = base64decode(oci_containerengine_cluster.oke_cluster.endpoints[0].public_endpoint_ca_certificate)
#   sensitive   = true
# }

# Kubernetes Module Variables
# This is a placeholder structure for the Kubernetes (OKE) module

variable "compartment_ocid" {
  description = "The OCID of the compartment where resources will be created"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "vcn_id" {
  description = "The OCID of the VCN where OKE cluster will be deployed"
  type        = string
}

variable "public_subnet_ids" {
  description = "List of public subnet OCIDs for load balancers"
  type        = list(string)
}

variable "private_subnet_ids" {
  description = "List of private subnet OCIDs for worker nodes"
  type        = list(string)
}

variable "kubernetes_version" {
  description = "Kubernetes version for the OKE cluster"
  type        = string
  default     = "v1.28.2"
}

variable "cluster_name" {
  description = "Name of the OKE cluster"
  type        = string
  default     = ""
}

variable "node_pools" {
  description = "List of node pool configurations"
  type = list(object({
    name                      = string
    enabled                   = optional(bool, true)
    node_shape                = string
    node_shape_memory_gb      = number
    node_shape_ocpus          = number
    initial_node_labels       = map(string)
    size                      = number
    max_size                  = number
    min_size                  = number
    boot_volume_size_gb       = number
    image_id                  = string
  }))
  default = [
    {
      name                      = "workers"
      enabled                   = true
      node_shape                = "VM.Standard.E4.Flex"
      node_shape_memory_gb      = 16
      node_shape_ocpus          = 2
      initial_node_labels       = {
        environment = "dev"
        managed-by  = "terragrunt"
        pool-type   = "workers"
      }
      size                      = 3
      max_size                  = 10
      min_size                  = 1
      boot_volume_size_gb       = 50
      image_id                  = ""
    }
  ]
}

variable "cluster_options" {
  description = "Optional cluster configuration"
  type = object({
    service_lb_subnet_ids                = list(string)
    add_ons = object({
      is_kubernetes_dashboard_enabled = bool
      is_tiller_enabled              = bool
    })
    admission_controller_options = object({
      is_pod_security_policy_enabled = bool
    })
    kubernetes_network_config = object({
      pods_cidr     = string
      services_cidr = string
    })
  })
  default = {
    service_lb_subnet_ids = []
    add_ons = {
      is_kubernetes_dashboard_enabled = false
      is_tiller_enabled              = false
    }
    admission_controller_options = {
      is_pod_security_policy_enabled = false
    }
    kubernetes_network_config = {
      pods_cidr     = "**********/16"
      services_cidr = "*********/16"
    }
  }
}

variable "cluster_endpoint_config" {
  description = "Cluster endpoint configuration"
  type = object({
    is_public_ip_enabled = bool
    subnet_id           = string
    nsg_ids             = list(string)
  })
  default = {
    is_public_ip_enabled = true
    subnet_id           = ""
    nsg_ids             = []
  }
}

variable "api_access_cidr" {
  description = "CIDR block allowed to access Kubernetes API"
  type        = string
  default     = "0.0.0.0/0"
}

variable "enable_autoscaling" {
  description = "Enable cluster autoscaling"
  type        = bool
  default     = true
}

variable "enable_monitoring" {
  description = "Enable cluster monitoring"
  type        = bool
  default     = true
}

variable "ssh_public_key" {
  description = "SSH public key for worker nodes"
  type        = string
  default     = ""
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

# TODO: Add more OKE-specific variables as needed
# - Cluster endpoint configuration
# - Image policy configuration
# - Cluster autoscaling settings
# - Monitoring and logging configuration

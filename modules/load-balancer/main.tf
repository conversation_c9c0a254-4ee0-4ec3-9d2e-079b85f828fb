# Load Balancer Module - Main Configuration
# This module creates an OCI Load Balancer for distributing traffic

# Create Load Balancer
resource "oci_load_balancer_load_balancer" "main" {
  compartment_id = var.compartment_ocid
  display_name   = "${var.project_name}-${var.environment}-lb"
  shape          = var.load_balancer_shape
  subnet_ids     = var.subnet_ids
  
  dynamic "shape_details" {
    for_each = var.load_balancer_shape == "flexible" ? [1] : []
    content {
      maximum_bandwidth_in_mbps = var.shape_details.maximum_bandwidth_in_mbps
      minimum_bandwidth_in_mbps = var.shape_details.minimum_bandwidth_in_mbps
    }
  }
  
  is_private                 = var.is_private
  network_security_group_ids = var.network_security_group_ids
  
  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-lb"
    Type = "load-balancer"
  })
}

# Create Backend Set
resource "oci_load_balancer_backend_set" "main" {
  for_each = var.backend_sets
  
  load_balancer_id = oci_load_balancer_load_balancer.main.id
  name             = each.key
  policy           = each.value.policy
  
  health_checker {
    protocol            = each.value.health_checker.protocol
    port                = each.value.health_checker.port
    url_path            = each.value.health_checker.url_path
    return_code         = each.value.health_checker.return_code
    interval_ms         = each.value.health_checker.interval_ms
    timeout_in_millis   = each.value.health_checker.timeout_in_millis
    retries             = each.value.health_checker.retries
  }
  
  dynamic "session_persistence_configuration" {
    for_each = each.value.session_persistence_enabled ? [1] : []
    content {
      cookie_name      = each.value.session_persistence_cookie_name
      disable_fallback = each.value.session_persistence_disable_fallback
    }
  }
  
  dynamic "ssl_configuration" {
    for_each = each.value.ssl_configuration != null ? [each.value.ssl_configuration] : []
    content {
      certificate_name        = ssl_configuration.value.certificate_name
      verify_depth           = ssl_configuration.value.verify_depth
      verify_peer_certificate = ssl_configuration.value.verify_peer_certificate
    }
  }
}

# Create Backends
resource "oci_load_balancer_backend" "main" {
  for_each = {
    for backend in flatten([
      for bs_name, bs_config in var.backend_sets : [
        for backend in bs_config.backends : {
          key           = "${bs_name}-${backend.ip_address}-${backend.port}"
          backend_set   = bs_name
          ip_address    = backend.ip_address
          port          = backend.port
          backup        = backend.backup
          drain         = backend.drain
          offline       = backend.offline
          weight        = backend.weight
        }
      ]
    ]) : backend.key => backend
  }
  
  load_balancer_id = oci_load_balancer_load_balancer.main.id
  backendset_name  = each.value.backend_set
  ip_address       = each.value.ip_address
  port             = each.value.port
  backup           = each.value.backup
  drain            = each.value.drain
  offline          = each.value.offline
  weight           = each.value.weight
}

# Create Listeners
resource "oci_load_balancer_listener" "main" {
  for_each = var.listeners

  load_balancer_id         = oci_load_balancer_load_balancer.main.id
  name                     = each.key
  default_backend_set_name = each.value.default_backend_set_name
  port                     = each.value.port
  protocol                 = each.value.protocol

  depends_on = [oci_load_balancer_backend_set.main]
  
  dynamic "ssl_configuration" {
    for_each = each.value.ssl_configuration != null ? [each.value.ssl_configuration] : []
    content {
      certificate_name        = ssl_configuration.value.certificate_name
      verify_depth           = ssl_configuration.value.verify_depth
      verify_peer_certificate = ssl_configuration.value.verify_peer_certificate
    }
  }
  
  dynamic "connection_configuration" {
    for_each = each.value.connection_configuration != null ? [each.value.connection_configuration] : []
    content {
      idle_timeout_in_seconds = connection_configuration.value.idle_timeout_in_seconds
    }
  }
}

# Create Certificates (if provided)
resource "oci_load_balancer_certificate" "main" {
  for_each = var.certificates
  
  load_balancer_id   = oci_load_balancer_load_balancer.main.id
  certificate_name   = each.key
  ca_certificate     = each.value.ca_certificate
  private_key        = each.value.private_key
  public_certificate = each.value.public_certificate
  passphrase         = each.value.passphrase
}

# Create Path Route Sets (for URL-based routing)
resource "oci_load_balancer_path_route_set" "main" {
  for_each = var.path_route_sets
  
  load_balancer_id = oci_load_balancer_load_balancer.main.id
  name             = each.key
  
  dynamic "path_routes" {
    for_each = each.value.path_routes
    content {
      backend_set_name = path_routes.value.backend_set_name
      path             = path_routes.value.path
      path_match_type {
        match_type = path_routes.value.path_match_type
      }
    }
  }
}

# Load Balancer Module Variables

variable "compartment_ocid" {
  description = "The OCID of the compartment where resources will be created"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet OCIDs where the load balancer will be created"
  type        = list(string)
}

variable "load_balancer_shape" {
  description = "Shape of the load balancer"
  type        = string
  default     = "100Mbps"
}

variable "shape_details" {
  description = "Shape details for flexible load balancer"
  type = object({
    maximum_bandwidth_in_mbps = number
    minimum_bandwidth_in_mbps = number
  })
  default = {
    maximum_bandwidth_in_mbps = 100
    minimum_bandwidth_in_mbps = 10
  }
}

variable "is_private" {
  description = "Whether the load balancer is private"
  type        = bool
  default     = false
}

variable "network_security_group_ids" {
  description = "List of network security group OCIDs"
  type        = list(string)
  default     = []
}

variable "backend_sets" {
  description = "Backend sets configuration"
  type = map(object({
    policy = string
    health_checker = object({
      protocol          = string
      port              = number
      url_path          = string
      return_code       = number
      interval_ms       = number
      timeout_in_millis = number
      retries           = number
    })
    backends = list(object({
      ip_address = string
      port       = number
      backup     = bool
      drain      = bool
      offline    = bool
      weight     = number
    }))
    session_persistence_enabled           = bool
    session_persistence_cookie_name       = string
    session_persistence_disable_fallback  = bool
    ssl_configuration = object({
      certificate_name        = string
      verify_depth           = number
      verify_peer_certificate = bool
    })
  }))
  default = {}
}

variable "listeners" {
  description = "Listeners configuration"
  type = map(object({
    default_backend_set_name = string
    port                     = number
    protocol                 = string
    ssl_configuration = object({
      certificate_name        = string
      verify_depth           = number
      verify_peer_certificate = bool
    })
    connection_configuration = object({
      idle_timeout_in_seconds = number
    })
  }))
  default = {}
}

variable "certificates" {
  description = "SSL certificates configuration"
  type = map(object({
    ca_certificate     = string
    private_key        = string
    public_certificate = string
    passphrase         = string
  }))
  default   = {}
  sensitive = true
}

variable "path_route_sets" {
  description = "Path route sets for URL-based routing"
  type = map(object({
    path_routes = list(object({
      backend_set_name  = string
      path              = string
      path_match_type   = string
    }))
  }))
  default = {}
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

# Network Module - Main Configuration

# Data source to get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_ocid
}

# Create VCN (Virtual Cloud Network)
resource "oci_core_vcn" "main" {
  compartment_id = var.compartment_ocid
  cidr_block     = var.vcn_cidr
  display_name   = "${var.project_name}-${var.environment}-vcn"
  dns_label      = "oraclevcn"

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-vcn"
  })
}

# Create Internet Gateway
resource "oci_core_internet_gateway" "main" {
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  display_name   = "${var.project_name}-${var.environment}-igw"
  enabled        = true

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-igw"
  })
}

# Create NAT Gateway for private subnets
resource "oci_core_nat_gateway" "main" {
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  display_name   = "${var.project_name}-${var.environment}-nat-gw"

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-nat-gw"
  })
}

# Create Service Gateway for OCI services
resource "oci_core_service_gateway" "main" {
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  display_name   = "${var.project_name}-${var.environment}-service-gw"

  services {
    service_id = data.oci_core_services.all_services.services[0].id
  }

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-service-gw"
  })
}

# Data source for OCI services
data "oci_core_services" "all_services" {
  filter {
    name   = "name"
    values = ["All .* Services In Oracle Services Network"]
    regex  = true
  }
}

# Create Public Route Table
resource "oci_core_route_table" "public" {
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  display_name   = "${var.project_name}-${var.environment}-public-rt"

  route_rules {
    destination       = "0.0.0.0/0"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_internet_gateway.main.id
  }

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-public-rt"
  })
}

# Create Private Route Table
resource "oci_core_route_table" "private" {
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  display_name   = "${var.project_name}-${var.environment}-private-rt"

  route_rules {
    destination       = "0.0.0.0/0"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_nat_gateway.main.id
  }

  route_rules {
    destination       = data.oci_core_services.all_services.services[0].cidr_block
    destination_type  = "SERVICE_CIDR_BLOCK"
    network_entity_id = oci_core_service_gateway.main.id
  }

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-private-rt"
  })
}

# Create Security Lists

# Public Security List
resource "oci_core_security_list" "public" {
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  display_name   = "${var.project_name}-${var.environment}-public-sl"

  # Egress rules - allow all outbound traffic
  egress_security_rules {
    destination = "0.0.0.0/0"
    protocol    = "all"
  }

  # Ingress rules for web traffic
  dynamic "ingress_security_rules" {
    for_each = var.security_rules.web
    content {
      protocol = "6" # TCP
      source   = ingress_security_rules.value.source

      tcp_options {
        min = ingress_security_rules.value.port
        max = ingress_security_rules.value.port
      }
    }
  }

  # Ingress rules for SSH
  dynamic "ingress_security_rules" {
    for_each = var.security_rules.ssh
    content {
      protocol = "6" # TCP
      source   = ingress_security_rules.value.source

      tcp_options {
        min = ingress_security_rules.value.port
        max = ingress_security_rules.value.port
      }
    }
  }

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-public-sl"
  })
}

# Private Security List
resource "oci_core_security_list" "private" {
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  display_name   = "${var.project_name}-${var.environment}-private-sl"

  # Egress rules - allow all outbound traffic
  egress_security_rules {
    destination = "0.0.0.0/0"
    protocol    = "all"
  }

  # Ingress rules for database traffic
  dynamic "ingress_security_rules" {
    for_each = var.security_rules.database
    content {
      protocol = "6" # TCP
      source   = ingress_security_rules.value.source

      tcp_options {
        min = ingress_security_rules.value.port
        max = ingress_security_rules.value.port
      }
    }
  }

  # Ingress rules for Kubernetes
  dynamic "ingress_security_rules" {
    for_each = var.security_rules.kubernetes
    content {
      protocol = "6" # TCP
      source   = ingress_security_rules.value.source

      tcp_options {
        min = ingress_security_rules.value.port
        max = ingress_security_rules.value.port
      }
    }
  }

  # Ingress rules for application traffic
  dynamic "ingress_security_rules" {
    for_each = var.security_rules.application
    content {
      protocol = "6" # TCP
      source   = ingress_security_rules.value.source

      tcp_options {
        min = ingress_security_rules.value.port
        max = ingress_security_rules.value.port
      }
    }
  }

  # Allow SSH from public subnets
  dynamic "ingress_security_rules" {
    for_each = var.security_rules.ssh
    content {
      protocol = "6" # TCP
      source   = ingress_security_rules.value.source

      tcp_options {
        min = ingress_security_rules.value.port
        max = ingress_security_rules.value.port
      }
    }
  }

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-private-sl"
  })
}

# Create Public Subnets
resource "oci_core_subnet" "public" {
  count          = length(var.public_subnets)
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  cidr_block     = var.public_subnets[count.index].cidr
  display_name   = "${var.project_name}-${var.environment}-${var.public_subnets[count.index].name}"
  dns_label      = "pub${count.index + 1}"

  availability_domain        = data.oci_identity_availability_domains.ads.availability_domains[var.public_subnets[count.index].availability_domain - 1].name
  route_table_id            = oci_core_route_table.public.id
  security_list_ids         = [oci_core_security_list.public.id]
  prohibit_public_ip_on_vnic = false

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-${var.public_subnets[count.index].name}"
    Type = "public"
  })
}

# Create a regional public subnet for OKE cluster endpoint
resource "oci_core_subnet" "oke_regional_subnet" {
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  cidr_block     = "*********/24"
  display_name   = "${var.project_name}-${var.environment}-oke-regional-subnet"
  dns_label      = "okeregional"

  # No availability_domain specified = regional subnet
  route_table_id             = oci_core_route_table.public.id
  security_list_ids          = [oci_core_security_list.public.id]
  prohibit_public_ip_on_vnic = false

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-oke-regional-subnet"
    Type = "oke-regional-subnet"
  })
}

# Create Private Subnets
resource "oci_core_subnet" "private" {
  count          = length(var.private_subnets)
  compartment_id = var.compartment_ocid
  vcn_id         = oci_core_vcn.main.id
  cidr_block     = var.private_subnets[count.index].cidr
  display_name   = "${var.project_name}-${var.environment}-${var.private_subnets[count.index].name}"
  dns_label      = "prv${count.index + 1}"

  availability_domain        = data.oci_identity_availability_domains.ads.availability_domains[var.private_subnets[count.index].availability_domain - 1].name
  route_table_id            = oci_core_route_table.private.id
  security_list_ids         = [oci_core_security_list.private.id]
  prohibit_public_ip_on_vnic = true

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-${var.private_subnets[count.index].name}"
    Type = "private"
  })
}

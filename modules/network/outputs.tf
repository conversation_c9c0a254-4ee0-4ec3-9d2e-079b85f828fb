# Network Module Outputs

# VCN Outputs
output "vcn_id" {
  description = "The OCID of the VCN"
  value       = oci_core_vcn.main.id
}

output "vcn_cidr_block" {
  description = "The CIDR block of the VCN"
  value       = oci_core_vcn.main.cidr_block
}

output "vcn_default_dhcp_options_id" {
  description = "The OCID of the default DHCP options"
  value       = oci_core_vcn.main.default_dhcp_options_id
}

output "vcn_default_security_list_id" {
  description = "The OCID of the default security list"
  value       = oci_core_vcn.main.default_security_list_id
}

# Gateway Outputs
output "internet_gateway_id" {
  description = "The OCID of the Internet Gateway"
  value       = oci_core_internet_gateway.main.id
}

output "nat_gateway_id" {
  description = "The OCID of the NAT Gateway"
  value       = oci_core_nat_gateway.main.id
}

output "service_gateway_id" {
  description = "The OCID of the Service Gateway"
  value       = oci_core_service_gateway.main.id
}

# Route Table Outputs
output "public_route_table_id" {
  description = "The OCID of the public route table"
  value       = oci_core_route_table.public.id
}

output "private_route_table_id" {
  description = "The OCID of the private route table"
  value       = oci_core_route_table.private.id
}

# Security List Outputs
output "public_security_list_id" {
  description = "The OCID of the public security list"
  value       = oci_core_security_list.public.id
}

output "private_security_list_id" {
  description = "The OCID of the private security list"
  value       = oci_core_security_list.private.id
}

# Public Subnet Outputs
output "public_subnet_ids" {
  description = "List of OCIDs of the public subnets"
  value       = oci_core_subnet.public[*].id
}

output "public_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the public subnets"
  value       = oci_core_subnet.public[*].cidr_block
}

output "public_subnet_names" {
  description = "List of names of the public subnets"
  value       = oci_core_subnet.public[*].display_name
}

# Private Subnet Outputs
output "private_subnet_ids" {
  description = "List of OCIDs of the private subnets"
  value       = oci_core_subnet.private[*].id
}

output "private_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the private subnets"
  value       = oci_core_subnet.private[*].cidr_block
}

output "private_subnet_names" {
  description = "List of names of the private subnets"
  value       = oci_core_subnet.private[*].display_name
}

# Availability Domain Outputs
output "availability_domains" {
  description = "List of availability domains"
  value       = data.oci_identity_availability_domains.ads.availability_domains[*].name
}

# Network Summary Output
output "network_summary" {
  description = "Summary of network configuration"
  value = {
    vcn_id                = oci_core_vcn.main.id
    vcn_cidr              = oci_core_vcn.main.cidr_block
    public_subnet_count   = length(oci_core_subnet.public)
    private_subnet_count  = length(oci_core_subnet.private)
    internet_gateway_id   = oci_core_internet_gateway.main.id
    nat_gateway_id        = oci_core_nat_gateway.main.id
    service_gateway_id    = oci_core_service_gateway.main.id
  }
}

# Network Module Variables

variable "compartment_ocid" {
  description = "The OCID of the compartment where resources will be created"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "vcn_cidr" {
  description = "CIDR block for the VCN"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnets" {
  description = "List of public subnet configurations"
  type = list(object({
    cidr                = string
    name                = string
    availability_domain = number
  }))
}

variable "private_subnets" {
  description = "List of private subnet configurations"
  type = list(object({
    cidr                = string
    name                = string
    availability_domain = number
  }))
}

variable "security_rules" {
  description = "Security rules configuration"
  type = object({
    web = list(object({
      port        = number
      protocol    = string
      source      = string
      description = string
    }))
    ssh = list(object({
      port        = number
      protocol    = string
      source      = string
      description = string
    }))
    database = list(object({
      port        = number
      protocol    = string
      source      = string
      description = string
    }))
    kubernetes = list(object({
      port        = number
      protocol    = string
      source      = string
      description = string
    }))
    application = list(object({
      port        = number
      protocol    = string
      source      = string
      description = string
    }))
  })
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VCN"
  type        = bool
  default     = true
}

variable "enable_dns_resolution" {
  description = "Enable DNS resolution in the VCN"
  type        = bool
  default     = true
}

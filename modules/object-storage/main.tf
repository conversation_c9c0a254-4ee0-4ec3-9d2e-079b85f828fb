# Object Storage Module - Main Configuration
# This module creates OCI Object Storage bucket for Terraform state management

# Object Storage Bucket for Terraform State
resource "oci_objectstorage_bucket" "terraform_state" {
  compartment_id = var.compartment_ocid
  name           = var.bucket_name
  namespace      = data.oci_objectstorage_namespace.ns.namespace

  # Enable versioning for state file history
  versioning = "Enabled"

  # Storage tier
  storage_tier = var.storage_tier

  # Object events
  object_events_enabled = var.object_events_enabled

  # Tags
  freeform_tags = merge(var.common_tags, {
    Name        = var.bucket_name
    Purpose     = "terraform-state"
    Environment = var.environment
  })
}

# Get the Object Storage namespace
data "oci_objectstorage_namespace" "ns" {
  compartment_id = var.compartment_ocid
}

# Create a test object to verify bucket functionality
resource "oci_objectstorage_object" "test_object" {
  bucket    = oci_objectstorage_bucket.terraform_state.name
  content   = "This bucket is ready for Terraform state storage"
  namespace = data.oci_objectstorage_namespace.ns.namespace
  object    = "test-connectivity.txt"

  # Tags
  metadata = {
    "created-by" = "terragrunt"
    "purpose"    = "connectivity-test"
  }
}

# Note: OCI Object Storage uses IAM policies for access control
# Bucket policies are not supported in the same way as AWS S3

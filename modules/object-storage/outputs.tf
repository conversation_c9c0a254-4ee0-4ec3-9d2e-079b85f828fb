# Object Storage Module Outputs

output "bucket_name" {
  description = "Name of the created Object Storage bucket"
  value       = oci_objectstorage_bucket.terraform_state.name
}

output "bucket_id" {
  description = "ID of the created Object Storage bucket"
  value       = oci_objectstorage_bucket.terraform_state.id
}

output "namespace" {
  description = "Object Storage namespace"
  value       = data.oci_objectstorage_namespace.ns.namespace
}

# Get current region from provider
data "oci_identity_region_subscriptions" "current_region" {
  tenancy_id = var.compartment_ocid
}

locals {
  current_region = data.oci_identity_region_subscriptions.current_region.region_subscriptions[0].region_name
}

output "bucket_url" {
  description = "URL of the Object Storage bucket"
  value       = "https://objectstorage.${local.current_region}.oraclecloud.com/n/${data.oci_objectstorage_namespace.ns.namespace}/b/${oci_objectstorage_bucket.terraform_state.name}"
}

output "s3_endpoint" {
  description = "S3-compatible endpoint for Terraform backend configuration"
  value       = "https://${data.oci_objectstorage_namespace.ns.namespace}.compat.objectstorage.${local.current_region}.oraclecloud.com"
}

output "terraform_backend_config" {
  description = "Terraform backend configuration for this bucket"
  value = {
    backend = "s3"
    config = {
      endpoint                    = "https://${data.oci_objectstorage_namespace.ns.namespace}.compat.objectstorage.${local.current_region}.oraclecloud.com"
      bucket                      = oci_objectstorage_bucket.terraform_state.name
      region                      = local.current_region
      encrypt                     = true
      skip_bucket_versioning      = false
      skip_credentials_validation = true
      skip_metadata_api_check     = true
      force_path_style           = true
    }
  }
  sensitive = false
}

output "bucket_summary" {
  description = "Summary of the Object Storage bucket configuration"
  value = {
    bucket_name       = oci_objectstorage_bucket.terraform_state.name
    namespace         = data.oci_objectstorage_namespace.ns.namespace
    region            = local.current_region
    storage_tier      = var.storage_tier
    versioning        = oci_objectstorage_bucket.terraform_state.versioning
    created_time      = oci_objectstorage_bucket.terraform_state.created_by
    environment       = var.environment
    project_name      = var.project_name
  }
}

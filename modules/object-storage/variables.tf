# Object Storage Module Variables

variable "compartment_ocid" {
  description = "The OCID of the compartment where the bucket will be created"
  type        = string
}

variable "bucket_name" {
  description = "Name of the Object Storage bucket for Terraform state"
  type        = string
}

# Note: region variable is provided by root.hcl provider configuration

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "storage_tier" {
  description = "Storage tier for the bucket (Standard, InfrequentAccess, Archive)"
  type        = string
  default     = "Standard"
}

variable "object_events_enabled" {
  description = "Enable object events for the bucket"
  type        = bool
  default     = false
}

# Note: Advanced features like retention rules and lifecycle policies
# can be configured separately using OCI-specific resources if needed

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

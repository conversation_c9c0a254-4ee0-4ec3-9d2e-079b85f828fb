# PostgreSQL Module - Main Configuration
# This module creates a PostgreSQL database using OCI PostgreSQL Database Service

# Data source to get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_ocid
}

# Generate a random password if not provided
resource "random_password" "db_password" {
  count   = var.db_password == "" ? 1 : 0
  length  = 16
  special = true
}

locals {
  db_password = var.db_password != "" ? var.db_password : random_password.db_password[0].result
}

# Create PostgreSQL Database System
resource "oci_psql_db_system" "postgresql" {
  compartment_id = var.compartment_ocid

  display_name = "${var.project_name}-${var.environment}-postgresql"

  db_version = var.db_version
  shape      = var.instance_shape

  # Network configuration
  network_details {
    subnet_id = var.private_subnet_ids[0]
    nsg_ids   = [oci_core_network_security_group.postgresql_nsg.id]
  }

  # Storage configuration
  storage_details {
    is_regionally_durable = false
    system_type          = "OCI_OPTIMIZED_STORAGE"
    availability_domain  = data.oci_identity_availability_domains.ads.availability_domains[0].name
  }

  # Database configuration
  credentials {
    username = var.db_username
    password_details {
      password_type = "PLAIN_TEXT"
      password      = local.db_password
    }
  }

  # Instance configuration
  instance_count       = 1

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-postgresql"
    Type = "database"
  })
}

# Note: OCI PostgreSQL Database Service has built-in backup capabilities
# Backup configuration is handled through the psql_db_system resource parameters

# Create security group for database access
resource "oci_core_network_security_group" "postgresql_nsg" {
  compartment_id = var.compartment_ocid
  vcn_id         = var.vcn_id
  display_name   = "${var.project_name}-${var.environment}-postgresql-nsg"

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-postgresql-nsg"
    Type = "network-security-group"
  })
}

# NSG rule for PostgreSQL access from application subnets
resource "oci_core_network_security_group_security_rule" "postgresql_ingress" {
  network_security_group_id = oci_core_network_security_group.postgresql_nsg.id
  direction                 = "INGRESS"
  protocol                  = "6" # TCP

  description = "Allow PostgreSQL access from VCN"
  source      = var.vcn_cidr
  source_type = "CIDR_BLOCK"

  tcp_options {
    destination_port_range {
      min = 5432
      max = 5432
    }
  }
}

# NSG rule for SSH access
resource "oci_core_network_security_group_security_rule" "postgresql_ssh" {
  count = var.ssh_public_key != "" ? 1 : 0

  network_security_group_id = oci_core_network_security_group.postgresql_nsg.id
  direction                 = "INGRESS"
  protocol                  = "6" # TCP

  description = "Allow SSH access for maintenance"
  source      = var.ssh_source_cidr
  source_type = "CIDR_BLOCK"

  tcp_options {
    destination_port_range {
      min = 22
      max = 22
    }
  }
}



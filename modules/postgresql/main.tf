# PostgreSQL Module - Main Configuration
# This module creates a PostgreSQL database using OCI Database System

# Data source to get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_ocid
}

# Generate a random password if not provided
resource "random_password" "db_password" {
  count   = var.db_password == "" ? 1 : 0
  length  = 16
  special = true
}

locals {
  db_password = var.db_password != "" ? var.db_password : random_password.db_password[0].result
}

# Create DB System for PostgreSQL
resource "oci_database_db_system" "postgresql" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_ocid
  database_edition    = "STANDARD_EDITION"

  db_home {
    database {
      admin_password = local.db_password
      db_name        = var.db_name
      character_set  = "AL32UTF8"
      ncharacter_set = "AL16UTF16"
      db_workload    = "OLTP"
      pdb_name       = var.db_name
    }

    db_version   = var.db_version
    display_name = "${var.project_name}-${var.environment}-db-home"
  }

  shape                   = var.instance_shape
  subnet_id              = var.private_subnet_ids[0]
  ssh_public_keys        = var.ssh_public_key != "" ? [var.ssh_public_key] : []
  display_name           = "${var.project_name}-${var.environment}-postgresql"
  hostname               = "${var.project_name}-${var.environment}-db"

  data_storage_size_in_gb = var.storage_size_gb
  license_model          = "LICENSE_INCLUDED"
  node_count             = 1

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-postgresql"
    Type = "database"
  })
}

# Create backup policy for the database
resource "oci_database_backup_policy" "postgresql_backup" {
  count = var.enable_backup ? 1 : 0

  compartment_id = var.compartment_ocid
  display_name   = "${var.project_name}-${var.environment}-db-backup-policy"

  backup_window_start_time = var.backup_window_start_time
  recovery_window_in_days  = var.backup_retention_days

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-db-backup-policy"
    Type = "backup-policy"
  })
}

# Create database backup destination
resource "oci_database_backup_destination" "postgresql_backup_dest" {
  count = var.enable_backup ? 1 : 0

  compartment_id = var.compartment_ocid
  display_name   = "${var.project_name}-${var.environment}-db-backup-dest"
  type           = "OBJECT_STORE"

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-db-backup-dest"
    Type = "backup-destination"
  })
}

# Create security group for database access
resource "oci_core_network_security_group" "postgresql_nsg" {
  compartment_id = var.compartment_ocid
  vcn_id         = var.vcn_id
  display_name   = "${var.project_name}-${var.environment}-postgresql-nsg"

  freeform_tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-postgresql-nsg"
    Type = "network-security-group"
  })
}

# NSG rule for PostgreSQL access from application subnets
resource "oci_core_network_security_group_security_rule" "postgresql_ingress" {
  network_security_group_id = oci_core_network_security_group.postgresql_nsg.id
  direction                 = "INGRESS"
  protocol                  = "6" # TCP

  description = "Allow PostgreSQL access from VCN"
  source      = var.vcn_cidr
  source_type = "CIDR_BLOCK"

  tcp_options {
    destination_port_range {
      min = 5432
      max = 5432
    }
  }
}

# NSG rule for SSH access
resource "oci_core_network_security_group_security_rule" "postgresql_ssh" {
  count = var.ssh_public_key != "" ? 1 : 0

  network_security_group_id = oci_core_network_security_group.postgresql_nsg.id
  direction                 = "INGRESS"
  protocol                  = "6" # TCP

  description = "Allow SSH access for maintenance"
  source      = var.ssh_source_cidr
  source_type = "CIDR_BLOCK"

  tcp_options {
    destination_port_range {
      min = 22
      max = 22
    }
  }
}

# Example of what the actual implementation might look like:
# 
# resource "oci_database_db_system" "postgresql" {
#   availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
#   compartment_id      = var.compartment_ocid
#   database_edition    = "STANDARD_EDITION"
#   
#   db_home {
#     database {
#       admin_password = var.db_password
#       db_name        = var.db_name
#       character_set  = "AL32UTF8"
#       ncharacter_set = "AL16UTF16"
#       db_workload    = "OLTP"
#       pdb_name       = var.db_name
#     }
#     
#     db_version   = var.db_version
#     display_name = "${var.project_name}-${var.environment}-db-home"
#   }
#   
#   shape                   = var.instance_shape
#   subnet_id              = var.private_subnet_ids[0]
#   ssh_public_keys        = [var.ssh_public_key]
#   display_name           = "${var.project_name}-${var.environment}-postgresql"
#   hostname               = "${var.project_name}-${var.environment}-db"
#   
#   data_storage_size_in_gb = var.storage_size_gb
#   license_model          = "LICENSE_INCLUDED"
#   node_count             = 1
#   
#   freeform_tags = merge(var.common_tags, {
#     Name = "${var.project_name}-${var.environment}-postgresql"
#     Type = "database"
#   })
# }

# PostgreSQL Module Outputs

output "db_system_id" {
  description = "The OCID of the DB system"
  value       = oci_database_db_system.postgresql.id
}

output "db_system_hostname" {
  description = "Hostname of the PostgreSQL database"
  value       = oci_database_db_system.postgresql.hostname
}

output "db_connection_string" {
  description = "Connection string for the PostgreSQL database"
  value       = "postgresql://${var.db_username}:${local.db_password}@${oci_database_db_system.postgresql.hostname}:5432/${var.db_name}"
  sensitive   = true
}

output "db_port" {
  description = "Port of the PostgreSQL database"
  value       = 5432
}

output "db_name" {
  description = "Name of the PostgreSQL database"
  value       = var.db_name
}

output "db_username" {
  description = "Username for the PostgreSQL database"
  value       = var.db_username
}

output "db_password" {
  description = "Password for the PostgreSQL database"
  value       = local.db_password
  sensitive   = true
}

output "network_security_group_id" {
  description = "OCID of the network security group for PostgreSQL"
  value       = oci_core_network_security_group.postgresql_nsg.id
}

output "backup_policy_id" {
  description = "OCID of the backup policy"
  value       = var.enable_backup ? oci_database_backup_policy.postgresql_backup[0].id : null
}

output "database_summary" {
  description = "Summary of database configuration"
  value = {
    db_system_id    = oci_database_db_system.postgresql.id
    hostname        = oci_database_db_system.postgresql.hostname
    db_name         = var.db_name
    db_username     = var.db_username
    port            = 5432
    storage_gb      = var.storage_size_gb
    backup_enabled  = var.enable_backup
    ha_enabled      = var.high_availability
  }
}

# Example of what the actual outputs might look like:
#
# output "db_system_id" {
#   description = "The OCID of the DB system"
#   value       = oci_database_db_system.postgresql.id
# }
#
# output "db_connection_string" {
#   description = "Connection string for the PostgreSQL database"
#   value       = "postgresql://${var.db_username}:${var.db_password}@${oci_database_db_system.postgresql.hostname}:5432/${var.db_name}"
#   sensitive   = true
# }
#
# output "db_hostname" {
#   description = "Hostname of the PostgreSQL database"
#   value       = oci_database_db_system.postgresql.hostname
# }
#
# output "db_port" {
#   description = "Port of the PostgreSQL database"
#   value       = 5432
# }
#
# output "db_name" {
#   description = "Name of the PostgreSQL database"
#   value       = var.db_name
# }
#
# output "db_username" {
#   description = "Username for the PostgreSQL database"
#   value       = var.db_username
# }

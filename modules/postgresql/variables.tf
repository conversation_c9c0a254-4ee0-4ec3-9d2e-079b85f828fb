# PostgreSQL Module Variables
# This is a placeholder structure for the PostgreSQL module

variable "compartment_ocid" {
  description = "The OCID of the compartment where resources will be created"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "vcn_id" {
  description = "The OCID of the VCN where PostgreSQL will be deployed"
  type        = string
}

variable "private_subnet_ids" {
  description = "List of private subnet OCIDs for PostgreSQL deployment"
  type        = list(string)
}

variable "db_name" {
  description = "Name of the PostgreSQL database"
  type        = string
  default     = "appdb"
}

variable "db_username" {
  description = "Master username for the PostgreSQL database"
  type        = string
  default     = "postgres"
}

variable "db_password" {
  description = "Master password for the PostgreSQL database"
  type        = string
  sensitive   = true
}

variable "db_version" {
  description = "PostgreSQL version"
  type        = string
  default     = "14"
}

variable "instance_shape" {
  description = "The shape of the DB system instance"
  type        = string
  default     = "VM.Standard2.1"
}

variable "storage_size_gb" {
  description = "Storage size in GB"
  type        = number
  default     = 256
}

variable "backup_retention_days" {
  description = "Number of days to retain backups"
  type        = number
  default     = 7
}

variable "common_tags" {
  description = "Common tags to be applied to all resources"
  type        = map(string)
  default     = {}
}

# TODO: Add more PostgreSQL-specific variables as needed
# - High availability configuration
# - Performance tuning parameters
# - Security configurations
# - Monitoring settings

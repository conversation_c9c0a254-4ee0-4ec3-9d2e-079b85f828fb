#!/bin/bash

# Oracle Cloud Infrastructure Management Script
# This script helps deploy, destroy, and manage all infrastructure modules

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Module deployment order (dependencies matter)
MODULES=(
    "object-storage"
    "network"
    "postgresql"
    "kubernetes"
    "cluster-autoscaler"
)

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if we're in the right directory
check_directory() {
    if [[ ! -f "common-vars.yaml" ]] || [[ ! -d "infrastructure" ]]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
}

# Function to deploy all modules
deploy_all() {
    local auto_approve="$1"
    
    print_status "🚀 Deploying all Oracle Cloud Infrastructure modules..."
    
    if [[ "$auto_approve" == "true" ]]; then
        print_warning "Auto-approve enabled - no manual confirmation required"
        terragrunt run-all apply --terragrunt-non-interactive
    else
        print_status "Manual approval required for each module"
        terragrunt run-all apply
    fi
    
    if [[ $? -eq 0 ]]; then
        print_success "✅ All modules deployed successfully!"
        
        # Sync states to OCI Object Storage
        print_status "📦 Syncing states to OCI Object Storage..."
        ./scripts/state-sync.sh sync-up
        
        # Show deployment summary
        show_deployment_summary
    else
        print_error "❌ Deployment failed!"
        exit 1
    fi
}

# Function to deploy modules sequentially
deploy_sequential() {
    local auto_approve="$1"
    local approve_flag=""
    
    if [[ "$auto_approve" == "true" ]]; then
        approve_flag="--auto-approve"
    fi
    
    print_status "🔄 Deploying modules sequentially..."
    
    for module in "${MODULES[@]}"; do
        if [[ -d "infrastructure/$module" ]]; then
            print_status "Deploying module: $module"
            
            cd "infrastructure/$module"
            
            if terragrunt apply $approve_flag; then
                print_success "✅ Module $module deployed successfully"
            else
                print_error "❌ Failed to deploy module: $module"
                cd ../..
                exit 1
            fi
            
            cd ../..
            
            # Sync state after each module
            ./scripts/state-sync.sh upload "$module" "infrastructure/$module/.terragrunt-cache/*/terraform.tfstate" 2>/dev/null || true
        else
            print_warning "Module directory not found: infrastructure/$module"
        fi
    done
    
    print_success "✅ All modules deployed sequentially!"
    show_deployment_summary
}

# Function to destroy all modules
destroy_all() {
    local auto_approve="$1"
    
    print_warning "🔥 DESTROYING all Oracle Cloud Infrastructure modules..."
    print_warning "This will DELETE all resources and data!"
    
    if [[ "$auto_approve" != "true" ]]; then
        read -p "Are you sure you want to destroy ALL resources? (type 'yes' to confirm): " confirm
        if [[ "$confirm" != "yes" ]]; then
            print_status "Destruction cancelled"
            return 0
        fi
    fi
    
    # Backup states before destruction
    print_status "📦 Backing up states before destruction..."
    ./scripts/state-sync.sh sync-up
    
    if [[ "$auto_approve" == "true" ]]; then
        terragrunt run-all destroy --terragrunt-non-interactive
    else
        terragrunt run-all destroy
    fi
    
    if [[ $? -eq 0 ]]; then
        print_success "✅ All modules destroyed successfully!"
    else
        print_error "❌ Destruction failed!"
        exit 1
    fi
}

# Function to destroy modules sequentially (reverse order)
destroy_sequential() {
    local auto_approve="$1"
    local approve_flag=""
    
    if [[ "$auto_approve" == "true" ]]; then
        approve_flag="--auto-approve"
    fi
    
    print_warning "🔥 Destroying modules sequentially (reverse order)..."
    
    if [[ "$auto_approve" != "true" ]]; then
        read -p "Are you sure you want to destroy resources? (type 'yes' to confirm): " confirm
        if [[ "$confirm" != "yes" ]]; then
            print_status "Destruction cancelled"
            return 0
        fi
    fi
    
    # Backup states before destruction
    ./scripts/state-sync.sh sync-up
    
    # Reverse the array for destruction
    local reversed_modules=()
    for ((i=${#MODULES[@]}-1; i>=0; i--)); do
        reversed_modules+=("${MODULES[i]}")
    done
    
    for module in "${reversed_modules[@]}"; do
        if [[ -d "infrastructure/$module" ]]; then
            print_status "Destroying module: $module"
            
            cd "infrastructure/$module"
            
            if terragrunt destroy $approve_flag; then
                print_success "✅ Module $module destroyed successfully"
            else
                print_warning "⚠️  Failed to destroy module: $module (may not exist)"
            fi
            
            cd ../..
        fi
    done
    
    print_success "✅ All modules destroyed sequentially!"
}

# Function to show deployment summary
show_deployment_summary() {
    print_status "📊 Deployment Summary:"
    echo "======================="
    
    for module in "${MODULES[@]}"; do
        if [[ -d "infrastructure/$module" ]]; then
            cd "infrastructure/$module"
            
            if terragrunt output &>/dev/null; then
                print_success "✅ $module: Deployed and healthy"
            else
                print_warning "⚠️  $module: May not be deployed"
            fi
            
            cd ../..
        fi
    done
    
    # Show Kubernetes cluster info if available
    if [[ -f ~/.kube/oracle-dev-oke-config ]]; then
        print_status "☸️  Kubernetes cluster status:"
        export KUBECONFIG=~/.kube/oracle-dev-oke-config
        ./scripts/kube-context.sh info 2>/dev/null || print_warning "Kubernetes cluster not accessible"
    fi
}

# Function to plan all modules
plan_all() {
    print_status "📋 Planning all modules..."
    terragrunt run-all plan
}

# Main script logic
main() {
    check_directory
    
    case "${1:-help}" in
        "deploy"|"up")
            if [[ "$2" == "--auto-approve" ]]; then
                deploy_all "true"
            else
                deploy_all "false"
            fi
            ;;
        "deploy-sequential"|"up-seq")
            if [[ "$2" == "--auto-approve" ]]; then
                deploy_sequential "true"
            else
                deploy_sequential "false"
            fi
            ;;
        "destroy"|"down")
            if [[ "$2" == "--auto-approve" ]]; then
                destroy_all "true"
            else
                destroy_all "false"
            fi
            ;;
        "destroy-sequential"|"down-seq")
            if [[ "$2" == "--auto-approve" ]]; then
                destroy_sequential "true"
            else
                destroy_sequential "false"
            fi
            ;;
        "plan")
            plan_all
            ;;
        "status"|"summary")
            show_deployment_summary
            ;;
        "help"|*)
            echo "Oracle Cloud Infrastructure Management Script"
            echo ""
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Commands:"
            echo "  deploy, up                    Deploy all modules (parallel)"
            echo "  deploy-sequential, up-seq     Deploy all modules (sequential)"
            echo "  destroy, down                 Destroy all modules (parallel)"
            echo "  destroy-sequential, down-seq  Destroy all modules (sequential)"
            echo "  plan                          Plan all modules"
            echo "  status, summary               Show deployment status"
            echo "  help                          Show this help message"
            echo ""
            echo "Options:"
            echo "  --auto-approve               Skip manual confirmation prompts"
            echo ""
            echo "Examples:"
            echo "  $0 deploy                     # Deploy all (with confirmations)"
            echo "  $0 deploy --auto-approve      # Deploy all (no confirmations)"
            echo "  $0 deploy-sequential          # Deploy one by one"
            echo "  $0 destroy --auto-approve     # Destroy all (no confirmations)"
            echo "  $0 plan                       # Plan all changes"
            echo "  $0 status                     # Show current status"
            echo ""
            echo "⚠️  CAUTION: destroy commands will DELETE all resources!"
            ;;
    esac
}

# Run main function with all arguments
main "$@"

#!/bin/bash

# Oracle Cloud Infrastructure Terraform/Terragrunt Setup Script
# This script installs all prerequisites for running Terraform and Terragrunt with OCI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if running on supported OS
check_os() {
    print_header "Checking Operating System"
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_status "Linux detected"
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_status "macOS detected"
        OS="darwin"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Install Terraform
install_terraform() {
    print_header "Installing Terraform"
    
    TERRAFORM_VERSION="1.6.6"
    
    if command -v terraform &> /dev/null; then
        CURRENT_VERSION=$(terraform version -json | jq -r '.terraform_version')
        print_status "Terraform is already installed (version: $CURRENT_VERSION)"
        return
    fi
    
    print_status "Installing Terraform version $TERRAFORM_VERSION..."
    
    if [[ "$OS" == "linux" ]]; then
        TERRAFORM_URL="https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip"
    else
        TERRAFORM_URL="https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_darwin_amd64.zip"
    fi
    
    # Create temporary directory
    TMP_DIR=$(mktemp -d)
    cd "$TMP_DIR"
    
    # Download and install Terraform
    curl -LO "$TERRAFORM_URL"
    unzip "terraform_${TERRAFORM_VERSION}_*.zip"
    sudo mv terraform /usr/local/bin/
    
    # Cleanup
    cd - > /dev/null
    rm -rf "$TMP_DIR"
    
    print_status "Terraform installed successfully"
    terraform version
}

# Install Terragrunt
install_terragrunt() {
    print_header "Installing Terragrunt"
    
    TERRAGRUNT_VERSION="0.54.8"
    
    if command -v terragrunt &> /dev/null; then
        CURRENT_VERSION=$(terragrunt --version | head -n1 | awk '{print $3}')
        print_status "Terragrunt is already installed (version: $CURRENT_VERSION)"
        return
    fi
    
    print_status "Installing Terragrunt version $TERRAGRUNT_VERSION..."
    
    if [[ "$OS" == "linux" ]]; then
        TERRAGRUNT_URL="https://github.com/gruntwork-io/terragrunt/releases/download/v${TERRAGRUNT_VERSION}/terragrunt_linux_amd64"
    else
        TERRAGRUNT_URL="https://github.com/gruntwork-io/terragrunt/releases/download/v${TERRAGRUNT_VERSION}/terragrunt_darwin_amd64"
    fi
    
    # Download and install Terragrunt
    curl -L "$TERRAGRUNT_URL" -o terragrunt
    chmod +x terragrunt
    sudo mv terragrunt /usr/local/bin/
    
    print_status "Terragrunt installed successfully"
    terragrunt --version
}

# Install OCI CLI
install_oci_cli() {
    print_header "Installing OCI CLI"
    
    if command -v oci &> /dev/null; then
        CURRENT_VERSION=$(oci --version 2>&1 | head -n1 | awk '{print $2}')
        print_status "OCI CLI is already installed (version: $CURRENT_VERSION)"
        return
    fi
    
    print_status "Installing OCI CLI..."
    
    # Install OCI CLI using the official installer
    bash -c "$(curl -L https://raw.githubusercontent.com/oracle/oci-cli/master/scripts/install/install.sh)" -- --accept-all-defaults
    
    # Add OCI CLI to PATH if not already there
    if ! command -v oci &> /dev/null; then
        echo 'export PATH=$PATH:~/bin' >> ~/.bashrc
        export PATH=$PATH:~/bin
    fi
    
    print_status "OCI CLI installed successfully"
    oci --version
}

# Install jq (required for JSON parsing)
install_jq() {
    print_header "Installing jq"
    
    if command -v jq &> /dev/null; then
        print_status "jq is already installed"
        return
    fi
    
    print_status "Installing jq..."
    
    if [[ "$OS" == "linux" ]]; then
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y jq
        elif command -v yum &> /dev/null; then
            sudo yum install -y jq
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y jq
        else
            print_error "Package manager not found. Please install jq manually."
            exit 1
        fi
    else
        if command -v brew &> /dev/null; then
            brew install jq
        else
            print_error "Homebrew not found. Please install jq manually."
            exit 1
        fi
    fi
    
    print_status "jq installed successfully"
}

# Install curl (if not present)
install_curl() {
    if ! command -v curl &> /dev/null; then
        print_status "Installing curl..."
        
        if [[ "$OS" == "linux" ]]; then
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y curl
            elif command -v yum &> /dev/null; then
                sudo yum install -y curl
            elif command -v dnf &> /dev/null; then
                sudo dnf install -y curl
            fi
        fi
    fi
}

# Install unzip (if not present)
install_unzip() {
    if ! command -v unzip &> /dev/null; then
        print_status "Installing unzip..."
        
        if [[ "$OS" == "linux" ]]; then
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y unzip
            elif command -v yum &> /dev/null; then
                sudo yum install -y unzip
            elif command -v dnf &> /dev/null; then
                sudo dnf install -y unzip
            fi
        fi
    fi
}

# Setup OCI configuration
setup_oci_config() {
    print_header "OCI Configuration Setup"
    
    if [[ -f ~/.oci/config ]]; then
        print_status "OCI configuration already exists at ~/.oci/config"
        print_warning "Please verify your OCI configuration manually"
        return
    fi
    
    print_status "Setting up OCI configuration..."
    print_warning "You will need the following information:"
    echo "  - Tenancy OCID"
    echo "  - User OCID"
    echo "  - Region"
    echo "  - API Key (will be generated if not exists)"
    echo ""
    
    read -p "Do you want to run OCI CLI setup now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        oci setup config
    else
        print_warning "Skipping OCI CLI setup. Run 'oci setup config' manually later."
    fi
}

# Verify installations
verify_installations() {
    print_header "Verifying Installations"
    
    print_status "Checking Terraform..."
    terraform version
    
    print_status "Checking Terragrunt..."
    terragrunt --version
    
    print_status "Checking OCI CLI..."
    oci --version
    
    print_status "Checking jq..."
    jq --version
    
    print_status "All tools installed successfully!"
}

# Main installation process
main() {
    print_header "Oracle Cloud Infrastructure Setup"
    print_status "This script will install Terraform, Terragrunt, and OCI CLI"
    
    check_os
    install_curl
    install_unzip
    install_jq
    install_terraform
    install_terragrunt
    install_oci_cli
    setup_oci_config
    verify_installations
    
    print_header "Installation Complete!"
    print_status "Next steps:"
    echo "1. Update common-vars.yaml with your OCI configuration"
    echo "2. Run 'terragrunt plan' to see what will be created"
    echo "3. Run 'terragrunt apply' to create the infrastructure"
    echo ""
    print_status "For detailed instructions, see README.md"
}

# Run main function
main "$@"

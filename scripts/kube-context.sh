#!/bin/bash

# Kubernetes Context Management Script for Oracle OKE
# This script helps manage kubectl contexts for different OKE clusters

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
KUBECONFIG_DIR="$HOME/.kube"
OKE_CONFIG_FILE="$KUBECONFIG_DIR/oracle-dev-oke-config"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if kubectl is installed
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    print_success "kubectl is available"
}

# Function to update kubeconfig from Terraform
update_kubeconfig() {
    print_status "Updating kubeconfig from Terraform output..."
    
    if [[ ! -d "infrastructure/kubernetes" ]]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
    
    cd infrastructure/kubernetes
    
    if ! terragrunt output -raw kubeconfig > "$OKE_CONFIG_FILE" 2>/dev/null; then
        print_error "Failed to get kubeconfig from Terraform. Make sure the cluster is deployed."
        exit 1
    fi
    
    cd ../..
    print_success "Kubeconfig updated: $OKE_CONFIG_FILE"
}

# Function to set the current context
set_context() {
    export KUBECONFIG="$OKE_CONFIG_FILE"
    print_success "KUBECONFIG set to: $OKE_CONFIG_FILE"
    
    # Test connectivity
    if kubectl cluster-info &>/dev/null; then
        print_success "Successfully connected to Oracle OKE cluster"
        kubectl get nodes --no-headers | wc -l | xargs printf "Cluster has %d node(s)\n"
    else
        print_warning "Could not connect to cluster. Check your OCI credentials and network connectivity."
    fi
}

# Function to show cluster information
show_info() {
    export KUBECONFIG="$OKE_CONFIG_FILE"
    
    print_status "Oracle OKE Cluster Information:"
    echo "================================"
    
    if kubectl cluster-info &>/dev/null; then
        echo "📋 Cluster Info:"
        kubectl cluster-info 2>/dev/null | grep -E "(Kubernetes|CoreDNS)" | sed 's/^/  /'
        
        echo ""
        echo "🖥️  Nodes:"
        kubectl get nodes -o custom-columns="NAME:.metadata.name,STATUS:.status.conditions[-1].type,ROLES:.metadata.labels.node-role\.kubernetes\.io/worker,AGE:.metadata.creationTimestamp,VERSION:.status.nodeInfo.kubeletVersion" --no-headers | sed 's/^/  /'
        
        echo ""
        echo "📦 System Pods:"
        kubectl get pods -n kube-system --no-headers | wc -l | xargs printf "  %d system pods running\n"
        
        echo ""
        echo "🔧 Current Context:"
        kubectl config current-context 2>/dev/null | sed 's/^/  /'
        
    else
        print_error "Cannot connect to cluster"
    fi
}

# Function to test cluster connectivity
test_connectivity() {
    export KUBECONFIG="$OKE_CONFIG_FILE"
    
    print_status "Testing cluster connectivity..."
    
    # Test API server connectivity
    if kubectl version --client=false &>/dev/null; then
        print_success "✅ API server is accessible"
    else
        print_error "❌ Cannot reach API server"
        return 1
    fi
    
    # Test node readiness
    local ready_nodes=$(kubectl get nodes --no-headers 2>/dev/null | grep -c " Ready " || echo "0")
    local total_nodes=$(kubectl get nodes --no-headers 2>/dev/null | wc -l || echo "0")
    
    if [[ "$ready_nodes" -gt 0 ]]; then
        print_success "✅ $ready_nodes/$total_nodes nodes are ready"
    else
        print_warning "⚠️  No nodes are ready"
    fi
    
    # Test system pods
    local running_pods=$(kubectl get pods -n kube-system --no-headers 2>/dev/null | grep -c " Running " || echo "0")
    local total_pods=$(kubectl get pods -n kube-system --no-headers 2>/dev/null | wc -l || echo "0")
    
    if [[ "$running_pods" -gt 0 ]]; then
        print_success "✅ $running_pods/$total_pods system pods are running"
    else
        print_warning "⚠️  System pods may not be ready"
    fi
}

# Function to backup current kubeconfig
backup_kubeconfig() {
    if [[ -f "$OKE_CONFIG_FILE" ]]; then
        local backup_file="$OKE_CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$OKE_CONFIG_FILE" "$backup_file"
        print_success "Kubeconfig backed up to: $backup_file"
    else
        print_warning "No existing kubeconfig to backup"
    fi
}

# Main script logic
main() {
    check_kubectl
    
    case "${1:-help}" in
        "update")
            backup_kubeconfig
            update_kubeconfig
            set_context
            ;;
        "set"|"use")
            set_context
            ;;
        "info"|"status")
            show_info
            ;;
        "test")
            test_connectivity
            ;;
        "backup")
            backup_kubeconfig
            ;;
        "help"|*)
            echo "Oracle OKE Kubernetes Context Management"
            echo ""
            echo "Usage: $0 <command>"
            echo ""
            echo "Commands:"
            echo "  update    Update kubeconfig from Terraform and set context"
            echo "  set       Set kubectl context to Oracle OKE cluster"
            echo "  info      Show cluster information and status"
            echo "  test      Test cluster connectivity and health"
            echo "  backup    Backup current kubeconfig"
            echo "  help      Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 update     # Update kubeconfig from Terraform"
            echo "  $0 set        # Set context to OKE cluster"
            echo "  $0 info       # Show cluster information"
            echo "  $0 test       # Test connectivity"
            ;;
    esac
}

# Run main function with all arguments
main "$@"

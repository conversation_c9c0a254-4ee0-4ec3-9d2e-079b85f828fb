#!/bin/bash

# OCI Object Storage State Sync Script
# This script syncs local Terraform state files to OCI Object Storage

set -e

# Configuration
BUCKET_NAME="oracle-dev-terraform-state"
NAMESPACE="axpq44vjev4y"
REGION="us-phoenix-1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if OCI CLI is configured
check_oci_cli() {
    if ! command -v oci &> /dev/null; then
        print_error "OCI CLI is not installed or not in PATH"
        exit 1
    fi
    
    if ! oci iam user get --user-id "$(oci iam user list --query 'data[0].id' --raw-output)" &> /dev/null; then
        print_error "OCI CLI is not properly configured"
        exit 1
    fi
    
    print_success "OCI CLI is properly configured"
}

# Function to backup existing state file before updating
backup_existing_state() {
    local module_name="$1"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local current_state="${module_name}/terraform.tfstate"
    local backup_name="${module_name}/backups/terraform.tfstate.${timestamp}"

    # Check if current state exists
    if oci os object head \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --name "$current_state" &> /dev/null; then

        print_status "Backing up existing state: $current_state -> $backup_name"

        # Download current state to temp file
        local temp_file="/tmp/current_state_${module_name}_${timestamp}.tfstate"

        if oci os object get \
            --bucket-name "$BUCKET_NAME" \
            --namespace "$NAMESPACE" \
            --name "$current_state" \
            --file "$temp_file" &> /dev/null; then

            # Upload as backup
            if oci os object put \
                --bucket-name "$BUCKET_NAME" \
                --namespace "$NAMESPACE" \
                --name "$backup_name" \
                --file "$temp_file" \
                --force &> /dev/null; then
                print_success "Existing state backed up: $backup_name"
            else
                print_warning "Failed to create backup of existing state"
            fi

            # Clean up temp file
            rm -f "$temp_file"
        else
            print_warning "Could not download existing state for backup"
        fi
    fi
}

# Function to upload state file to OCI Object Storage
upload_state() {
    local module_name="$1"
    local state_file="$2"

    if [[ ! -f "$state_file" ]]; then
        print_warning "State file not found: $state_file"
        return 1
    fi

    local object_name="${module_name}/terraform.tfstate"

    # Backup existing state before updating
    backup_existing_state "$module_name"

    print_status "Uploading state file: $state_file -> $object_name"

    if oci os object put \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --name "$object_name" \
        --file "$state_file" \
        --force; then
        print_success "State file uploaded successfully: $object_name"

        # Also create a timestamped backup of the new state
        local timestamp=$(date +"%Y%m%d_%H%M%S")
        local backup_name="${module_name}/backups/terraform.tfstate.${timestamp}"

        oci os object put \
            --bucket-name "$BUCKET_NAME" \
            --namespace "$NAMESPACE" \
            --name "$backup_name" \
            --file "$state_file" \
            --force &> /dev/null

        print_success "New state backup created: $backup_name"
    else
        print_error "Failed to upload state file"
        return 1
    fi
}

# Function to download state file from OCI Object Storage
download_state() {
    local module_path="$1"
    local state_file="$2"
    
    local object_name="${module_path}/terraform.tfstate"
    
    print_status "Downloading state file: $object_name -> $state_file"
    
    if oci os object get \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --name "$object_name" \
        --file "$state_file"; then
        print_success "State file downloaded successfully"
    else
        print_warning "State file not found in OCI Object Storage: $object_name"
        return 1
    fi
}

# Function to list state files in OCI Object Storage
list_states() {
    print_status "Listing state files in OCI Object Storage bucket: $BUCKET_NAME"

    oci os object list \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --query 'data[?ends_with(name, `.tfstate`)].{Name:name, Size:size, Modified:"time-modified"}' \
        --output table
}



# Function to find actual state files in terragrunt cache
find_module_state_files() {
    local modules_found=()

    # Find all terragrunt.hcl files (indicating modules)
    while IFS= read -r -d '' terragrunt_file; do
        local module_dir=$(dirname "$terragrunt_file")
        local module_name=$(basename "$module_dir")

        # Look for actual state files in .terragrunt-cache
        local cache_dir="$module_dir/.terragrunt-cache"
        if [[ -d "$cache_dir" ]]; then
            # Find the actual terraform.tfstate file
            local state_file=$(find "$cache_dir" -name "terraform.tfstate" -type f | head -1)
            if [[ -n "$state_file" && -f "$state_file" ]]; then
                modules_found+=("$module_name:$state_file")
            fi
        fi
    done < <(find infrastructure -name "terragrunt.hcl" -type f -print0)

    printf '%s\n' "${modules_found[@]}"
}

# Function to sync all module states
sync_all() {
    local action="$1"  # upload or download

    print_status "Syncing all module states ($action)"

    if [[ "$action" == "upload" ]]; then
        # Find all modules with state files
        local modules_with_states=($(find_module_state_files))

        if [[ ${#modules_with_states[@]} -eq 0 ]]; then
            print_warning "No modules with state files found"
            return 1
        fi

        for module_info in "${modules_with_states[@]}"; do
            local module_name="${module_info%%:*}"
            local state_file="${module_info##*:}"

            print_status "Processing module: $module_name"
            print_status "State file: $state_file"

            upload_state "$module_name" "$state_file"
        done

    elif [[ "$action" == "download" ]]; then
        # Find all terragrunt.hcl files for download targets
        find infrastructure -name "terragrunt.hcl" -type f | while read -r terragrunt_file; do
            local module_dir=$(dirname "$terragrunt_file")
            local module_name=$(basename "$module_dir")
            local target_file="$module_dir/terraform.tfstate"

            print_status "Processing module: $module_name"
            download_state "$module_name" "$target_file"
        done
    fi
}

# Main script logic
main() {
    check_oci_cli
    
    case "${1:-help}" in
        "upload")
            if [[ -n "$2" && -n "$3" ]]; then
                upload_state "$2" "$3"
            else
                sync_all "upload"
            fi
            ;;
        "download")
            if [[ -n "$2" && -n "$3" ]]; then
                download_state "$2" "$3"
            else
                sync_all "download"
            fi
            ;;
        "list")
            list_states
            ;;
        "sync-up")
            sync_all "upload"
            ;;
        "sync-down")
            sync_all "download"
            ;;
        "help"|*)
            echo "OCI Object Storage State Sync Script"
            echo ""
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Commands:"
            echo "  upload <module> <state_file>  Upload specific state file"
            echo "  download <module> <state_file> Download specific state file"
            echo "  sync-up                       Upload all module states (with backup)"
            echo "  sync-down                     Download all module states"
            echo "  list                          List all state files in bucket"
            echo "  help                          Show this help message"
            echo ""
            echo "Features:"
            echo "  • Automatic backup of existing states before updating"
            echo "  • Module-based organization (network/, kubernetes/, etc.)"
            echo "  • Timestamped backups for recovery"
            echo "  • Clean module-wise state management"
            echo ""
            echo "Examples:"
            echo "  $0 upload network infrastructure/network/terraform.tfstate"
            echo "  $0 download network infrastructure/network/terraform.tfstate"
            echo "  $0 sync-up      # Upload all states with backup"
            echo "  $0 sync-down    # Download all states"
            echo "  $0 list         # Show all state files"
            ;;
    esac
}

# Run main function with all arguments
main "$@"

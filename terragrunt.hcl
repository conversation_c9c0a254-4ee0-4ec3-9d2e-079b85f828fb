# Root Terragrunt Configuration

# Configure Terragrunt to use local state for now
# Uncomment the remote_state block below after creating the OCI Object Storage bucket
#
# remote_state {
#   backend = "s3"
#   config = {
#     # OCI Object Storage S3 compatibility endpoint
#     endpoint                    = "https://${local.common_vars.oci.region}.compat.objectstorage.${local.common_vars.oci.region}.oraclecloud.com"
#     bucket                      = "${local.common_vars.environment.project_name}-${local.common_vars.environment.name}-terraform-state"
#     key                         = "${path_relative_to_include()}/terraform.tfstate"
#     region                      = local.common_vars.oci.region
#     encrypt                     = true
#     skip_bucket_versioning      = false
#     skip_credentials_validation = true
#     skip_metadata_api_check     = true
#     force_path_style           = true
#   }
#   generate = {
#     path      = "backend.tf"
#     if_exists = "overwrite_terragrunt"
#   }
# }

# Generate an OCI provider block (only provider configuration, not terraform block)
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
# Configure the Oracle Cloud Infrastructure Provider
provider "oci" {
  tenancy_ocid     = "${local.common_vars.oci.tenancy_ocid}"
  user_ocid        = "${local.common_vars.oci.user_ocid}"
  fingerprint      = "${local.common_vars.oci.fingerprint}"
  private_key_path = "${local.common_vars.oci.private_key_path}"
  region           = "${local.common_vars.oci.region}"
}
EOF
}

# Load common variables
locals {
  # Use path_relative_to_include() to handle both root and subdirectory execution
  common_vars_path = fileexists("${get_terragrunt_dir()}/common-vars.yaml") ? "${get_terragrunt_dir()}/common-vars.yaml" : "${find_in_parent_folders("common-vars.yaml")}"
  common_vars = yamldecode(file(local.common_vars_path))
}

# Configure common inputs that will be passed to all child terragrunt configurations
inputs = {
  # OCI Configuration
  tenancy_ocid     = local.common_vars.oci.tenancy_ocid
  user_ocid        = local.common_vars.oci.user_ocid
  fingerprint      = local.common_vars.oci.fingerprint
  private_key_path = local.common_vars.oci.private_key_path
  region           = local.common_vars.oci.region
  compartment_ocid = local.common_vars.oci.compartment_ocid
  
  # Environment Configuration
  environment   = local.common_vars.environment.name
  project_name  = local.common_vars.environment.project_name
  
  # Network Configuration
  vcn_cidr        = local.common_vars.network.vcn_cidr
  public_subnets  = local.common_vars.network.public_subnets
  private_subnets = local.common_vars.network.private_subnets
  security_rules  = local.common_vars.security_rules
  
  # Common Tags
  common_tags = local.common_vars.common_tags
}

# Retry configuration for transient errors
retryable_errors = [
  "(?s).*Error.*timeout.*",
  "(?s).*Error.*connection.*reset.*",
  "(?s).*Error.*connection.*refused.*",
  "(?s).*Error.*500.*Internal.*Server.*Error.*",
  "(?s).*Error.*502.*Bad.*Gateway.*",
  "(?s).*Error.*503.*Service.*Unavailable.*",
  "(?s).*Error.*504.*Gateway.*Timeout.*"
]

# Terraform configuration
terraform {
  extra_arguments "common_vars" {
    commands = get_terraform_commands_that_need_vars()
  }

  extra_arguments "disable_input" {
    commands  = get_terraform_commands_that_need_input()
    arguments = ["-input=false"]
  }

  extra_arguments "retry_lock" {
    commands  = get_terraform_commands_that_need_locking()
    arguments = ["-lock-timeout=20m"]
  }
}

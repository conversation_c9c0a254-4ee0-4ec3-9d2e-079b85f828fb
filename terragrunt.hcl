# PostgreSQL Module Configuration

# Load common variables
locals {
  common_vars = yamldecode(file(find_in_parent_folders("common-vars.yaml")))
  env_vars    = local.common_vars.environment
  pg_vars     = local.common_vars.postgresql
}

# Generate provider configuration
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
provider "oci" {
  tenancy_ocid     = "${local.common_vars.oci.tenancy_ocid}"
  user_ocid        = "${local.common_vars.oci.user_ocid}"
  fingerprint      = "${local.common_vars.oci.fingerprint}"
  private_key_path = "${local.common_vars.oci.private_key_path}"
  region           = "${local.common_vars.oci.region}"
}
EOF
}

# Specify the Terraform module source
terraform {
  source = "../../modules/postgresql"
}

# Dependency on network module
dependency "network" {
  config_path = "../network"

  mock_outputs = {
    vcn_id             = "mock-vcn-id"
    private_subnet_ids = ["mock-subnet-1", "mock-subnet-2", "mock-subnet-3"]
    public_subnet_ids  = ["mock-subnet-1", "mock-subnet-2", "mock-subnet-3"]
  }
}

# PostgreSQL module inputs
inputs = {
  # Network dependencies
  vcn_id             = dependency.network.outputs.vcn_id
  private_subnet_ids = dependency.network.outputs.private_subnet_ids
  public_subnet_ids  = dependency.network.outputs.public_subnet_ids

  # PostgreSQL specific configuration from common vars
  db_name     = local.pg_vars.db_name
  db_username = local.pg_vars.db_username
  db_password = local.pg_vars.db_password
  db_version  = local.pg_vars.db_version

  # Instance configuration from common vars
  instance_shape           = local.pg_vars.instance_shape
  storage_size_gb          = local.pg_vars.storage_size_gb
  backup_retention_days    = local.pg_vars.backup_retention_days
  enable_backup            = local.pg_vars.enable_backup
  backup_window_start_time = local.pg_vars.backup_window_start_time

  # Security configuration from common vars
  ssh_public_key     = local.pg_vars.ssh_public_key
  ssh_source_cidr    = "10.0.0.0/16"
  high_availability  = local.pg_vars.high_availability
  monitoring_enabled = local.pg_vars.monitoring_enabled

  # Common tags from common vars
  common_tags = local.env_vars.common_tags
}


